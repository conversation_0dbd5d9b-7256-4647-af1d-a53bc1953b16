<?php
namespace FaithConnectSpace\Core\Utils;

use FaithConnectSpace\Core\Utils\File_Manager;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * Logger class for theme.
 */
class Logger {

	private static $log_path;

	/**
	 * Logger constructor.
	 */
	public function __construct() {
		$upload_dir = File_Manager::get_upload_path( 'theme-log' );
		File_Manager::create_folder( $upload_dir );

		self::$log_path = $upload_dir . '/theme-log.log';
	}

	/**
	 * Get theme log file URL.
	 *
	 * @return string Theme log file URL.
	 */
	public static function get_theme_log_url() {
		return File_Manager::get_upload_path( 'theme-log', 'theme-log.log', true );
	}

	/**
	 * Log message with specific level.
	 *
	 * @param string $message Log message.
	 * @param string $level Log level (e.g., INFO, ERROR, WARNING).
	 * @param array  $context Additional context data (optional).
	 */
	public static function log( $message, $level = 'INFO', $context = array() ) {
		$time = date( 'Y-m-d H:i:s' );

		$context = ! empty( $context ) ? json_encode( $context ) : '';

		$formatted_message = sprintf(
			"[%s] [%s] %s %s\n",
			$time,
			$level,
			$message,
			$context
		);

		error_log( $formatted_message, 3, self::$log_path );
	}

	/**
	 * Log debug message.
	 *
	 * @param string $message Log message.
	 * @param array $context Additional context data (optional).
	 */
	public static function debug( $message, $context = array() ) {
		self::log( $message, 'DEBUG', $context );
	}

	/**
	 * Log notice message.
	 *
	 * @param string $message Log message.
	 * @param array $context Additional context data (optional).
	 */
	public static function notice( $message, $context = array() ) {
		self::log( $message, 'NOTICE', $context );
	}

	/**
	 * Log info message.
	 *
	 * @param string $message Log message.
	 * @param array $context Additional context data (optional).
	 */
	public static function info( $message, $context = array() ) {
		self::log( $message, 'INFO', $context );
	}

	/**
	 * Log warning message.
	 *
	 * @param string $message Log message.
	 * @param array $context Additional context data (optional).
	 */
	public static function warning( $message, $context = array() ) {
		self::log( $message, 'WARNING', $context );
	}

	/**
	 * Log error message.
	 *
	 * @param string $message Log message.
	 * @param array $context Additional context data (optional).
	 */
	public static function error( $message, $context = array() ) {
		self::log( $message, 'ERROR', $context );
	}

	/**
	 * Log alert message.
	 *
	 * @param string $message Log message.
	 * @param array $context Additional context data (optional).
	 */
	public static function alert( $message, $context = array() ) {
		self::log( $message, 'ALERT', $context );
	}

	/**
	 * Log emergency message.
	 *
	 * @param string $message Log message.
	 * @param array $context Additional context data (optional).
	 */
	public static function emergency( $message, $context = array() ) {
		self::log( $message, 'EMERGENCY', $context );
	}

	/**
	 * Log critical message.
	 *
	 * @param string $message Log message.
	 * @param array $context Additional context data (optional).
	 */
	public static function critical( $message, $context = array() ) {
		self::log( $message, 'CRITICAL', $context );
	}

}
