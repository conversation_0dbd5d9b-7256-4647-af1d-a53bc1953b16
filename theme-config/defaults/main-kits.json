{"system_colors": [{"_id": "primary", "title": "Primary", "color": "#404F40"}, {"_id": "secondary", "title": "Secondary", "color": "#1D1D1D"}, {"_id": "text", "title": "Text", "color": "#252628"}, {"_id": "accent", "title": "Accent", "color": "#ED5A2F"}, {"_id": "tertiary", "title": "Alternate Accent", "color": "#968B87"}, {"_id": "background", "title": "Background", "color": "#FFFFFF"}, {"_id": "alternate", "title": "Alternate Background", "color": "#F8F1E6"}, {"_id": "border", "title": "Border", "color": "#DEDEDE"}], "custom_colors": [{"_id": "08e9b58", "title": "Custom 1", "color": "#FE9446"}, {"_id": "8fba85b", "title": "Custom 2", "color": "#FFC646"}, {"_id": "0b79062", "title": "Custom 3", "color": "#C5B2FF"}], "system_typography": [{"_id": "primary", "title": "Primary", "typography_typography": "custom", "typography_font_family": "Bebas Neue Local", "typography_font_weight": "400"}, {"_id": "secondary", "title": "Secondary", "typography_typography": "custom", "typography_font_family": "<PERSON> Local", "typography_font_weight": "600"}, {"_id": "text", "title": "Text", "typography_typography": "custom", "typography_font_family": "Lora Local", "typography_font_weight": "400", "typography_font_size": {"unit": "px", "size": "18", "sizes": []}, "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.555", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 17, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 16, "sizes": []}}, {"_id": "accent", "title": "Accent", "typography_typography": "custom", "typography_font_family": "<PERSON> Condensed Local", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": "18", "sizes": []}, "typography_text_transform": "uppercase", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.444", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": 1, "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 16, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 14, "sizes": []}}, {"_id": "tertiary", "title": "Alternate Accent", "typography_typography": "custom", "typography_font_family": "<PERSON> Local", "typography_font_size": {"unit": "px", "size": "16", "sizes": []}, "typography_font_weight": "500", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.5", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 15, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 14, "sizes": []}}, {"_id": "meta", "title": "Meta", "typography_typography": "custom", "typography_font_family": "<PERSON> Local", "typography_font_size": {"unit": "px", "size": "16", "sizes": []}, "typography_font_weight": "600", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.5", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 15, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 14, "sizes": []}}, {"_id": "taxonomy", "title": "Alternate Meta", "typography_typography": "custom", "typography_font_family": "<PERSON> Local", "typography_font_size": {"unit": "px", "size": "13", "sizes": []}, "typography_font_weight": "600", "typography_text_transform": "uppercase", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.55", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "1", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 12, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 11, "sizes": []}}, {"_id": "small", "title": "Small", "typography_typography": "custom", "typography_font_family": "Lora Local", "typography_font_size": {"unit": "px", "size": "16", "sizes": []}, "typography_font_weight": "400", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.5", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 15, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 14, "sizes": []}}, {"_id": "h1", "title": "H1", "typography_typography": "custom", "typography_font_family": "Bebas Neue Local", "typography_font_size": {"unit": "px", "size": "150", "sizes": []}, "typography_font_weight": "400", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": 0.85, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "-2", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 100, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 70, "sizes": []}}, {"_id": "h2", "title": "H2", "typography_typography": "custom", "typography_font_family": "Bebas Neue Local", "typography_font_size": {"unit": "px", "size": "100", "sizes": []}, "typography_font_weight": "400", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": 0.9, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "-1", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 76, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 56, "sizes": []}}, {"_id": "h3", "title": "H3", "typography_typography": "custom", "typography_font_family": "Bebas Neue Local", "typography_font_size": {"unit": "px", "size": "74", "sizes": []}, "typography_font_weight": "400", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": 0.95, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "-1", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 52, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 40, "sizes": []}}, {"_id": "h4", "title": "H4", "typography_typography": "custom", "typography_font_family": "<PERSON> Local", "typography_font_size": {"unit": "px", "size": "32", "sizes": []}, "typography_font_weight": "600", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.25", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "-1", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 28, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 22, "sizes": []}}, {"_id": "h5", "title": "H5", "typography_typography": "custom", "typography_font_family": "<PERSON> Local", "typography_font_size": {"unit": "px", "size": "24", "sizes": []}, "typography_font_weight": "600", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.33", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 20, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 18, "sizes": []}}, {"_id": "h6", "title": "H6", "typography_typography": "custom", "typography_font_family": "<PERSON> Condensed Local", "typography_font_size": {"unit": "px", "size": "18", "sizes": []}, "typography_font_weight": "600", "typography_text_transform": "uppercase", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.44", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "2", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 16, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 14, "sizes": []}}, {"_id": "button", "title": "<PERSON><PERSON>", "typography_typography": "custom", "typography_font_family": "<PERSON> Condensed Local", "typography_font_size": {"unit": "px", "size": "20", "sizes": []}, "typography_font_weight": "700", "typography_text_transform": "uppercase", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.4", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 18, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 16, "sizes": []}}, {"_id": "blockquote", "title": "Blockquote", "typography_typography": "custom", "typography_font_family": "Lora Local", "typography_font_size": {"unit": "px", "size": "36", "sizes": []}, "typography_font_weight": "400", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": "1.3", "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_word_spacing": {"unit": "px", "size": "0", "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 28, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 24, "sizes": []}}], "custom_typography": [{"_id": "0b2392a", "title": "Excerpt", "typography_typography": "custom", "typography_font_family": "Lora Local", "typography_font_weight": "400", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": 1.5, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": 0, "sizes": []}, "typography_word_spacing": {"unit": "px", "size": 0, "sizes": []}, "typography_font_size": {"unit": "px", "size": 21, "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 20, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 18, "sizes": []}}, {"_id": "9280a98", "title": "Custom 1", "typography_typography": "custom", "typography_font_family": "Abuget Local", "typography_font_size": {"unit": "px", "size": 56, "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 44, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 36, "sizes": []}, "typography_font_weight": "400", "typography_text_transform": "none", "typography_font_style": "normal", "typography_text_decoration": "none", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": 0, "sizes": []}, "typography_word_spacing": {"unit": "px", "size": 0, "sizes": []}}], "default_generic_fonts": "Sans-serif", "container_width": {"unit": "px", "size": 1280, "sizes": []}, "container_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "page_title_selector": "h1.entry-title", "active_breakpoints": ["viewport_mobile", "viewport_tablet", "viewport_widescreen"], "viewport_tablet": 1180, "viewport_widescreen": 1601, "cmsmasters_header_top_info_items": [], "cmsmasters_header_top_social_items": [], "cmsmasters_header_mid_info_items": [], "cmsmasters_header_mid_social_items": [], "cmsmasters_header_bot_social_items": [], "cmsmasters_archive_grid_columns": "", "cmsmasters_search_grid_columns": "", "cmsmasters_footer_social_items": [], "cmsmasters_footer_info_items": [], "viewport_md": 768, "viewport_lg": 1181, "colors_enable_styleguide_preview": "yes", "typography_enable_styleguide_preview": "yes", "activeItemIndex": 1, "__globals__": {"cmsmasters_heading_title_color": "globals/colors?id=background", "cmsmasters_button_normal_colors_bg": "globals/colors?id=accent", "cmsmasters_button_normal_colors_color": "globals/colors?id=background", "cmsmasters_button_normal_colors_bd": "globals/colors?id=accent", "cmsmasters_button_hover_colors_bd": "globals/colors?id=border", "cmsmasters_button_hover_colors_bg": "globals/colors?id=background", "cmsmasters_button_hover_colors_color": "globals/colors?id=secondary", "cmsmasters_lazyload_widget_preloader_background_color": "globals/colors?id=background", "cmsmasters_lazyload_widget_preloader_border_color": "globals/colors?id=border", "cmsmasters_sidebar_widgets_title_typography_typography": "globals/typography?id=h4"}, "woocommerce_cart_page_id": "45478", "woocommerce_checkout_page_id": "45483", "woocommerce_myaccount_page_id": "45471", "cmsmasters_main_layout": "fullwidth", "cmsmasters_button_normal_bd_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "cmsmasters_button_normal_border_border": "solid", "cmsmasters_button_normal_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "cmsmasters_button_padding": {"unit": "px", "top": "15", "right": "40", "bottom": "15", "left": "40", "isLinked": false}, "site_name": "Faith Connect Main", "cmsmasters_archive_layout": "fullwidth", "cmsmasters_lazyload_widget_preloader_icon": {"value": {"url": "https://faith-connect.cmsmasters.net/main/wp-content/uploads/sites/10/2025/03/preloader.svg", "id": 45827}, "library": "svg"}, "cmsmasters_lazyload_widget_preloader_icon_size": {"unit": "px", "size": 60, "sizes": []}, "cmsmasters_lazyload_widget_preloader_height": {"unit": "px", "size": 480, "sizes": []}, "cmsmasters_lazyload_widget_preloader_background_background": "classic", "cmsmasters_lazyload_widget_preloader_border_border": "solid", "cmsmasters_lazyload_widget_preloader_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "cmsmasters_lazyload_widget_preloader_bd_radius": {"unit": "px", "size": 19, "sizes": []}, "cmsmasters_lazyload_widget_preloader_height_tablet": {"unit": "px", "size": 280, "sizes": []}, "cmsmasters_lazyload_widget_preloader_height_mobile": {"unit": "px", "size": 380, "sizes": []}}