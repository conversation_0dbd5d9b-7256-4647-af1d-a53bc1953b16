/**
 * Global styles
 */
.pswp__button, .pswp__button:hover {
  border: 0;
  border-radius: 0;
}
.pswp__button:before, .pswp__button:after {
  content: none;
}

p.demo_store,
.woocommerce-store-notice {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0;
  width: 100%;
  font-size: 1em;
  padding: 1em 0;
  text-align: center;
  background-color: var(--cmsmasters-colors-heading);
  color: var(--cmsmasters-colors-bg);
  z-index: 99998;
  display: none;
}
p.demo_store a,
.woocommerce-store-notice a {
  color: var(--cmsmasters-colors-link);
  text-decoration: underline;
}
p.demo_store a:hover,
.woocommerce-store-notice a:hover {
  color: var(--cmsmasters-colors-bg);
}

.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  position: absolute !important;
  width: 1px;
  word-wrap: normal !important;
}

.admin-bar p.demo_store {
  top: 32px;
}

/**
 * Utility classes
 */
.clear {
  clear: both;
}

/**
 * Main WooCommerce styles
 */
.woocommerce {
  /**
   * Product Page
   */
  /**
   * Product loops
   */
  /**
   * Buttons
   */
  /**
   * Reviews
   */
  /**
   * Star ratings
   */
  /**
   * Tables
   */
  /**
   * Cart sidebar
   */
  /**
   * Forms
   */
  /**
   * Order page
   */
  /**
   * Layered nav widget
   */
  /**
   * Price filter widget
   */
  /**
   * Rating Filter Widget
   */
}
.woocommerce .blockUI.blockOverlay {
  position: relative;
}
.woocommerce .blockUI.blockOverlay:before,
.woocommerce .loader:before {
  content: "\e931";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2em;
  color: var(--cmsmasters-colors-heading);
  display: block;
  width: 1em;
  height: 1em;
  margin: auto;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  animation: spin 1s ease-in-out infinite;
}
.woocommerce .price ins {
  text-decoration: none;
}
.woocommerce a.remove {
  font-size: 15px;
  text-align: center;
  text-decoration: none;
  text-indent: -9999px;
  color: var(--cmsmasters-colors-link);
  background-color: transparent;
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-colors-link);
  display: block;
  width: 1.4em;
  height: 1.4em;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}
.woocommerce a.remove:before {
  text-indent: 0;
  content: "\e87f";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.woocommerce a.remove:hover {
  color: var(--cmsmasters-colors-hover);
  background-color: transparent;
  border-color: var(--cmsmasters-colors-hover);
}
.woocommerce small.note {
  display: block;
  color: var(--cmsmasters-colors-text);
  font-size: 0.857em;
  margin-top: 10px;
}
.woocommerce .woocommerce-breadcrumb {
  margin: 0 0 1em;
  padding: 0;
  font-size: 0.92em;
  color: var(--cmsmasters-colors-text);
}
.woocommerce .woocommerce-breadcrumb:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce .woocommerce-breadcrumb a {
  color: var(--cmsmasters-colors-text);
}
.woocommerce .select2-container .select2-selection--single {
  background-image: none;
  outline: none;
  height: auto;
  width: 100%;
  max-width: 100%;
  vertical-align: middle;
  flex-grow: 1;
  text-shadow: none;
  font-family: var(--cmsmasters-input-font-family);
  font-weight: var(--cmsmasters-input-font-weight);
  font-style: var(--cmsmasters-input-font-style);
  text-transform: var(--cmsmasters-input-text-transform);
  text-decoration: var(--cmsmasters-input-text-decoration);
  font-size: var(--cmsmasters-input-font-size);
  line-height: var(--cmsmasters-input-line-height);
  letter-spacing: var(--cmsmasters-input-letter-spacing);
  word-spacing: var(--cmsmasters-input-word-spacing);
  color: var(--cmsmasters-input-normal-colors-color);
  background-color: var(--cmsmasters-input-normal-colors-bg);
  border-color: var(--cmsmasters-input-normal-colors-bd);
  border-style: var(--cmsmasters-input-normal-bd-style);
  border-top-width: var(--cmsmasters-input-normal-bd-width-top);
  border-right-width: var(--cmsmasters-input-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-input-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-input-normal-bd-width-left);
  border-radius: var(--cmsmasters-input-normal-bd-radius);
  box-shadow: var(--cmsmasters-input-normal-box-shadow);
  padding-top: var(--cmsmasters-input-padding-top);
  padding-right: var(--cmsmasters-input-padding-right);
  padding-bottom: var(--cmsmasters-input-padding-bottom);
  padding-left: var(--cmsmasters-input-padding-left);
  transition: all 0.3s ease-in-out;
  margin: 0;
  position: relative;
}
.woocommerce .select2-container .select2-selection--single::-webkit-input-placeholder {
  color: var(--cmsmasters-input-placeholder-color);
  transition: color 0.2s ease-in-out;
}
.woocommerce .select2-container .select2-selection--single::-moz-placeholder {
  color: var(--cmsmasters-input-placeholder-color);
  transition: color 0.2s ease-in-out;
}
.woocommerce .select2-container .select2-selection--single:focus {
  color: var(--cmsmasters-input-focus-colors-color);
  background-color: var(--cmsmasters-input-focus-colors-bg);
  border-color: var(--cmsmasters-input-focus-colors-bd);
  border-radius: var(--cmsmasters-input-focus-bd-radius);
  box-shadow: var(--cmsmasters-input-focus-box-shadow);
}
.woocommerce .select2-container .select2-selection--single:focus::-webkit-input-placeholder {
  color: transparent;
}
.woocommerce .select2-container .select2-selection--single:focus::-moz-placeholder {
  color: transparent;
}
.woocommerce .select2-container .select2-selection--single .select2-selection__rendered {
  color: inherit;
  line-height: inherit;
  padding: 0;
}
.woocommerce .select2-container .select2-selection--single .select2-selection__arrow {
  width: 8px;
  height: 5px;
  margin: auto !important;
  position: absolute;
  left: var(--cmsmasters-input-padding-right);
  right: auto;
  top: 0;
  bottom: 0;
}
.woocommerce .select2-container .select2-selection--single .select2-selection__arrow b {
  width: 100%;
  height: 100%;
  border-top-color: var(--cmsmasters-input-normal-colors-color);
}
.woocommerce .select2-container--open .select2-selection--single {
  color: var(--cmsmasters-input-focus-colors-color);
  background-color: var(--cmsmasters-input-focus-colors-bg);
  border-color: var(--cmsmasters-input-focus-colors-bd);
  border-radius: var(--cmsmasters-input-focus-bd-radius);
  box-shadow: var(--cmsmasters-input-focus-box-shadow);
}
.woocommerce .select2-container--open.select2-container--above .select2-selection--single {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.woocommerce .select2-container--open.select2-container--below .select2-selection--single {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.woocommerce .quantity .qty {
  width: 5em;
  max-width: 100%;
}
.woocommerce dl.variation {
  margin: 10px 0;
  overflow: hidden;
}
.woocommerce dl.variation:last-child {
  margin-bottom: 0;
}
.woocommerce dl.variation dt,
.woocommerce dl.variation dd {
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
  float: right;
  margin: 0;
  padding: 0;
}
.woocommerce dl.variation dt {
  clear: right;
}
.woocommerce dl.variation dd {
  padding: 0 5px 0 0;
}
.woocommerce dl.variation dd p {
  margin: 0;
  padding: 0;
}
.woocommerce div.product {
  margin-bottom: 0;
  position: relative;
}
.woocommerce div.product .product_title {
  clear: none;
  margin-top: 0;
  padding: 0;
}
.woocommerce div.product span.price,
.woocommerce div.product p.price {
  color: var(--cmsmasters-colors-link);
  font-size: 1.25em;
}
.woocommerce div.product span.price ins,
.woocommerce div.product p.price ins {
  background: inherit;
  display: inline-block;
}
.woocommerce div.product span.price del,
.woocommerce div.product p.price del {
  font-size: 0.9em;
  color: var(--cmsmasters-colors-hover);
  display: inline-block;
}
.woocommerce div.product span.price del + ins,
.woocommerce div.product p.price del + ins {
  margin-right: 5px;
}
.woocommerce div.product p.stock {
  font-size: 0.92em;
}
.woocommerce div.product .stock {
  color: var(--cmsmasters-colors-link);
}
.woocommerce div.product .out-of-stock {
  color: var(--cmsmasters-colors-link);
}
.woocommerce div.product .woocommerce-product-rating {
  margin-bottom: 1.618em;
}
.woocommerce div.product div.images {
  margin-bottom: 3rem;
}
.woocommerce div.product div.images img {
  display: block;
  width: 100%;
  height: auto;
  box-shadow: none;
}
.woocommerce div.product div.images div.thumbnails {
  padding-top: 1em;
}
.woocommerce div.product div.images.woocommerce-product-gallery {
  position: relative;
}
.woocommerce div.product div.images .woocommerce-product-gallery__wrapper {
  transition: all cubic-bezier(0.795, -0.035, 0, 1) 0.5s;
  margin: 0;
  padding: 0;
}
.woocommerce div.product div.images .woocommerce-product-gallery__wrapper .zoomImg {
  background-color: #fff;
  opacity: 0;
}
.woocommerce div.product div.images .woocommerce-product-gallery__image--placeholder {
  border: 1px solid #f2f2f2;
}
.woocommerce div.product div.images .woocommerce-product-gallery__image:nth-child(n+2) {
  width: 25%;
  display: inline-block;
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger {
  background-color: var(--cmsmasters-colors-bg);
  color: var(--cmsmasters-colors-heading);
  font-size: var(--cmsmasters-accent-font-size);
  text-indent: -9999px;
  width: calc(var(--cmsmasters-accent-line-height) + 1em);
  height: calc(var(--cmsmasters-accent-line-height) + 1em);
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 9;
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger:hover {
  color: var(--cmsmasters-colors-link);
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger::before {
  content: "\e94a";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.5em;
  text-indent: 0;
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.woocommerce div.product div.images .flex-control-thumbs {
  overflow: hidden;
  zoom: 1;
  margin: 0 -2px 0 0;
  padding: 0;
}
.woocommerce div.product div.images .flex-control-thumbs li {
  width: 25%;
  float: left;
  margin: 0;
  padding: 2px 2px 0 0;
  list-style: none;
}
.woocommerce div.product div.images .flex-control-thumbs li img {
  cursor: pointer;
  opacity: 0.5;
  margin: 0;
}
.woocommerce div.product div.images .flex-control-thumbs li img.flex-active, .woocommerce div.product div.images .flex-control-thumbs li img:hover {
  opacity: 1;
}
.woocommerce div.product .woocommerce-product-gallery--columns-3 .flex-control-thumbs li:nth-child(3n+1) {
  clear: left;
}
.woocommerce div.product .woocommerce-product-gallery--columns-4 .flex-control-thumbs li:nth-child(4n+1) {
  clear: left;
}
.woocommerce div.product .woocommerce-product-gallery--columns-5 .flex-control-thumbs li:nth-child(5n+1) {
  clear: left;
}
.woocommerce div.product div.summary {
  margin-bottom: 3rem;
}
.woocommerce div.product div.social {
  text-align: right;
  margin: 0 0 1em;
}
.woocommerce div.product div.social span {
  margin: 0 0 0 2px;
}
.woocommerce div.product div.social span span {
  margin: 0;
}
.woocommerce div.product div.social span .stButton .chicklets {
  padding-left: 16px;
  width: 0;
}
.woocommerce div.product div.social iframe {
  float: left;
  margin-top: 3px;
}
.woocommerce div.product .woocommerce-tabs {
  margin: 3rem 0 0;
}
.woocommerce div.product .woocommerce-tabs ul.tabs {
  list-style: none;
  padding: 0;
  margin: -10px -5px 1.5rem;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  position: relative;
}
.woocommerce div.product .woocommerce-tabs ul.tabs li {
  flex-grow: 1;
  margin: 10px 5px 0;
  padding: 0;
}
@media only screen and (min-width: ELEMENTOR_SCREEN_DESKTOP_MIN) {
  .woocommerce div.product .woocommerce-tabs ul.tabs li {
    flex-grow: 0;
  }
}
.woocommerce div.product .woocommerce-tabs ul.tabs li a {
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  text-decoration: none;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  background-color: var(--cmsmasters-colors-alternate);
  color: var(--cmsmasters-colors-heading);
  display: block;
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out;
}
.woocommerce div.product .woocommerce-tabs ul.tabs li a:hover {
  text-decoration: none;
  color: var(--cmsmasters-colors-link);
}
.woocommerce div.product .woocommerce-tabs ul.tabs li.active a {
  background-color: var(--cmsmasters-colors-bg);
  color: var(--cmsmasters-colors-link);
}
.woocommerce div.product .woocommerce-tabs .panel {
  margin: 0;
  padding: 0;
}
.woocommerce div.product .woocommerce-tabs .panel h2 {
  font-family: var(--cmsmasters-h4-font-family);
  font-weight: var(--cmsmasters-h4-font-weight);
  font-style: var(--cmsmasters-h4-font-style);
  text-transform: var(--cmsmasters-h4-text-transform);
  text-decoration: var(--cmsmasters-h4-text-decoration);
  font-size: var(--cmsmasters-h4-font-size);
  line-height: var(--cmsmasters-h4-line-height);
  letter-spacing: var(--cmsmasters-h4-letter-spacing);
  word-spacing: var(--cmsmasters-h4-word-spacing);
  margin-bottom: 1.5rem;
}
.woocommerce div.product .woocommerce-tabs .panel h2 + table {
  margin-top: 0;
}
.woocommerce div.product .woocommerce-tabs .panel > *:first-child {
  margin-top: 0;
}
.woocommerce div.product .woocommerce-tabs .panel > *:last-child {
  margin-bottom: 0;
}
.woocommerce div.product p.cart {
  margin-bottom: 1.5rem;
}
.woocommerce div.product p.cart:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce div.product form.cart {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.woocommerce div.product form.cart:not(.variations_form):not(.grouped_form) {
  display: flex;
}
.woocommerce div.product form.cart:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce div.product form.cart .woocommerce-variation-add-to-cart {
  display: flex;
}
.woocommerce div.product form.cart div.quantity {
  margin-left: 10px;
}
.woocommerce div.product form.cart table {
  border: 0;
  margin: 0;
}
.woocommerce div.product form.cart table th,
.woocommerce div.product form.cart table td {
  background-color: transparent;
  border: 0;
  padding: 0;
}
.woocommerce div.product form.cart table div.quantity {
  float: none;
  margin: 0;
}
.woocommerce div.product form.cart table label {
  font-family: var(--cmsmasters-base-font-family);
  font-weight: var(--cmsmasters-base-font-weight);
  font-style: var(--cmsmasters-base-font-style);
  text-transform: var(--cmsmasters-base-text-transform);
  text-decoration: var(--cmsmasters-base-text-decoration);
  font-size: var(--cmsmasters-base-font-size);
  line-height: var(--cmsmasters-base-line-height);
  letter-spacing: var(--cmsmasters-base-letter-spacing);
  word-spacing: var(--cmsmasters-base-word-spacing);
  margin: 0;
}
.woocommerce div.product form.cart table p {
  margin: 0;
}
.woocommerce div.product form.cart table small.stock {
  display: block;
  float: none;
}
.woocommerce div.product form.cart .variations {
  margin-bottom: 1em;
  width: 100%;
}
.woocommerce div.product form.cart .variations tr:not(:first-child) th,
.woocommerce div.product form.cart .variations tr:not(:first-child) td {
  padding-top: 1em;
}
.woocommerce div.product form.cart .variations td,
.woocommerce div.product form.cart .variations th {
  color: var(--cmsmasters-colors-text);
  border: 0;
  background-color: transparent;
  vertical-align: baseline;
  padding: 0;
}
.woocommerce div.product form.cart .variations select {
  max-width: 100%;
  min-width: 75%;
  display: inline-block;
  margin-right: 1em;
}
.woocommerce div.product form.cart .variations td.label {
  padding-right: 1em;
}
.woocommerce div.product form.cart .woocommerce-variation > div {
  margin-bottom: 1em;
}
.woocommerce div.product form.cart .woocommerce-variation > div > *:first-child {
  margin-top: 0;
}
.woocommerce div.product form.cart .woocommerce-variation > div > *:last-child {
  margin-bottom: 0;
}
.woocommerce div.product form.cart .reset_variations {
  color: var(--cmsmasters-colors-link);
  display: inline-block;
  margin-top: 1em;
  visibility: hidden;
}
.woocommerce div.product form.cart .reset_variations:hover {
  color: var(--cmsmasters-colors-hover);
}
.woocommerce div.product form.cart .wc-no-matching-variations {
  display: none;
}
.woocommerce div.product form.cart .button {
  vertical-align: middle;
  float: left;
}
.woocommerce div.product form.cart .group_table {
  margin-bottom: 2em;
}
.woocommerce div.product form.cart .group_table tr:not(:first-child) > * {
  padding-top: 1em;
}
.woocommerce div.product form.cart .group_table td.woocommerce-grouped-product-list-item__label {
  padding-right: 1em;
  padding-left: 1em;
}
.woocommerce div.product form.cart .group_table td:first-child {
  width: 4em;
}
.woocommerce div.product form.cart .group_table td.woocommerce-grouped-product-list-item__price del {
  color: var(--cmsmasters-colors-hover);
}
.woocommerce div.product form.cart .group_table td.woocommerce-grouped-product-list-item__price ins {
  margin-right: 5px;
  text-decoration: none;
}
.woocommerce div.product form.cart .group_table .wc-grouped-product-add-to-cart-checkbox {
  display: inline-block;
  width: auto;
  margin: 0 auto;
}
.woocommerce div.product > .products {
  margin-top: 3rem;
}
.woocommerce div.product > .products > h2 {
  font-family: var(--cmsmasters-h4-font-family);
  font-weight: var(--cmsmasters-h4-font-weight);
  font-style: var(--cmsmasters-h4-font-style);
  text-transform: var(--cmsmasters-h4-text-transform);
  text-decoration: var(--cmsmasters-h4-text-decoration);
  font-size: var(--cmsmasters-h4-font-size);
  line-height: var(--cmsmasters-h4-line-height);
  letter-spacing: var(--cmsmasters-h4-letter-spacing);
  word-spacing: var(--cmsmasters-h4-word-spacing);
}
.woocommerce .product_meta > span {
  display: block;
}
.woocommerce .product_meta > span:not(:first-child) {
  margin-top: 5px;
}
.woocommerce span.onsale {
  font-family: var(--cmsmasters-accent-font-family);
  font-weight: var(--cmsmasters-accent-font-weight);
  font-style: var(--cmsmasters-accent-font-style);
  text-transform: var(--cmsmasters-accent-text-transform);
  text-decoration: var(--cmsmasters-accent-text-decoration);
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
  letter-spacing: var(--cmsmasters-accent-letter-spacing);
  word-spacing: var(--cmsmasters-accent-word-spacing);
  text-align: center;
  padding: 0.5em 1em;
  margin: 0;
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: var(--cmsmasters-colors-link);
  color: var(--cmsmasters-colors-bg);
  z-index: 9;
}
.woocommerce .products ul,
.woocommerce ul.products {
  margin: 0;
  padding: 0;
  list-style: none outside;
  clear: both;
}
.woocommerce .products ul:after,
.woocommerce ul.products:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce .products ul li,
.woocommerce ul.products li {
  list-style: none outside;
}
.woocommerce ul.products {
  margin-bottom: -2.992em;
  overflow: hidden;
}
.woocommerce ul.products li.product .onsale {
  outline: none;
}
.woocommerce ul.products li.product h3,
.woocommerce ul.products li.product .woocommerce-loop-product__title,
.woocommerce ul.products li.product .woocommerce-loop-category__title {
  font-family: var(--cmsmasters-h5-font-family);
  font-weight: var(--cmsmasters-h5-font-weight);
  font-style: var(--cmsmasters-h5-font-style);
  text-transform: var(--cmsmasters-h5-text-transform);
  text-decoration: var(--cmsmasters-h5-text-decoration);
  font-size: var(--cmsmasters-h5-font-size);
  line-height: var(--cmsmasters-h5-line-height);
  letter-spacing: var(--cmsmasters-h5-letter-spacing);
  word-spacing: var(--cmsmasters-h5-word-spacing);
  padding: 0;
  margin: 0.5em 0;
}
.woocommerce ul.products li.product a {
  text-decoration: none;
}
.woocommerce ul.products li.product a img {
  width: 100%;
  height: auto;
  display: block;
  margin: 0 0 1em;
  box-shadow: none;
}
.woocommerce ul.products li.product strong {
  display: block;
}
.woocommerce ul.products li.product .woocommerce-placeholder {
  border: 1px solid #f2f2f2;
}
.woocommerce ul.products li.product .star-rating {
  font-size: 0.8em;
}
.woocommerce ul.products li.product .add_to_cart_button {
  display: block;
}
.woocommerce ul.products li.product .add_to_cart_button.added {
  display: none;
}
.woocommerce ul.products li.product .add_to_cart_button.added + .added_to_cart {
  opacity: 1;
}
.woocommerce ul.products li.product .added_to_cart {
  opacity: 0;
  transition: all 0.3s ease-in-out;
  display: block;
}
.woocommerce ul.products li.product .button {
  display: block;
  margin-top: 1em;
}
.woocommerce ul.products li.product .price {
  color: var(--cmsmasters-colors-link);
  display: block;
  font-weight: normal;
  margin-bottom: 0.5em;
  font-size: 0.857em;
}
.woocommerce ul.products li.product .price del {
  font-size: 0.9em;
  color: var(--cmsmasters-colors-hover);
  display: inline-block;
}
.woocommerce ul.products li.product .price del + ins {
  margin-right: 5px;
}
.woocommerce ul.products li.product .price ins {
  background: none;
  display: inline-block;
}
.woocommerce ul.products li.product .price .from {
  font-size: 0.67em;
  margin: -2px 0 0 0;
  text-transform: uppercase;
  color: var(--cmsmasters-colors-text);
}
.woocommerce ul.products li.product .count {
  background-color: transparent;
  color: var(--cmsmasters-colors-link);
}
.woocommerce .woocommerce-result-count {
  font-size: var(--cmsmasters-input-font-size);
  line-height: var(--cmsmasters-input-line-height);
  margin-top: calc(
			var(--cmsmasters-input-padding-top) +
			var(--cmsmasters-input-normal-bd-width-top)
		);
  margin-bottom: calc(
			var(--cmsmasters-input-padding-bottom) +
			var(--cmsmasters-input-normal-bd-width-bottom) +
			1em
		);
}
.woocommerce .woocommerce-ordering {
  margin: 0 0 1em;
}
.woocommerce .woocommerce-ordering select {
  vertical-align: top;
}
.woocommerce nav.woocommerce-pagination {
  text-align: center;
}
.woocommerce nav.woocommerce-pagination ul {
  display: inline-block;
  white-space: nowrap;
  padding: 0;
  clear: both;
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  border-right: 0;
  margin: 1px;
}
.woocommerce nav.woocommerce-pagination ul li {
  border-left-width: 1px;
  border-left-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  padding: 0;
  margin: 0;
  float: left;
  display: inline;
  overflow: hidden;
}
.woocommerce nav.woocommerce-pagination ul li a,
.woocommerce nav.woocommerce-pagination ul li span {
  margin: 0;
  text-decoration: none;
  padding: 0;
  line-height: 1;
  font-size: 1em;
  font-weight: normal;
  padding: 0.5em;
  min-width: 1em;
  display: block;
}
.woocommerce nav.woocommerce-pagination ul li span.current,
.woocommerce nav.woocommerce-pagination ul li a:hover,
.woocommerce nav.woocommerce-pagination ul li a:focus {
  background-color: var(--cmsmasters-colors-link);
  color: var(--cmsmasters-colors-bg);
}
.woocommerce a.button,
.woocommerce button.button {
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  color: var(--cmsmasters-button-normal-colors-color);
  border-color: var(--cmsmasters-button-normal-colors-bd);
  border-style: var(--cmsmasters-button-normal-bd-style);
  border-top-width: var(--cmsmasters-button-normal-bd-width-top);
  border-right-width: var(--cmsmasters-button-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-button-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-button-normal-bd-width-left);
  border-radius: var(--cmsmasters-button-normal-bd-radius);
  text-shadow: var(--cmsmasters-button-text-shadow);
  box-shadow: var(--cmsmasters-button-normal-box-shadow);
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  display: inline-block;
  cursor: pointer;
  word-break: normal;
  position: relative;
  z-index: 0;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  background-color: transparent;
  background-image: none;
}
.woocommerce a.button:hover,
.woocommerce button.button:hover {
  color: var(--cmsmasters-button-hover-colors-color);
  border-color: var(--cmsmasters-button-hover-colors-bd);
  border-radius: var(--cmsmasters-button-hover-bd-radius);
  text-decoration: var(--cmsmasters-button-hover-text-decoration);
  text-shadow: var(--cmsmasters-button-hover-text-shadow);
  box-shadow: var(--cmsmasters-button-hover-box-shadow);
}
.woocommerce a.button:before, .woocommerce a.button:after,
.woocommerce button.button:before,
.woocommerce button.button:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
}
.woocommerce a.button:before,
.woocommerce button.button:before {
  background-color: var(--cmsmasters-button-normal-colors-bg);
  background-image: var(--cmsmasters-button-normal-colors-bg-image);
  opacity: 1;
}
.woocommerce a.button:after,
.woocommerce button.button:after {
  background-color: var(--cmsmasters-button-hover-colors-bg);
  background-image: var(--cmsmasters-button-hover-colors-bg-image);
  opacity: 0;
}
.woocommerce a.button:hover:before,
.woocommerce button.button:hover:before {
  opacity: 0;
}
.woocommerce a.button:hover:after,
.woocommerce button.button:hover:after {
  opacity: 1;
}
.woocommerce input.button,
.woocommerce #respond input#submit {
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  color: var(--cmsmasters-button-normal-colors-color);
  border-color: var(--cmsmasters-button-normal-colors-bd);
  border-style: var(--cmsmasters-button-normal-bd-style);
  border-top-width: var(--cmsmasters-button-normal-bd-width-top);
  border-right-width: var(--cmsmasters-button-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-button-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-button-normal-bd-width-left);
  border-radius: var(--cmsmasters-button-normal-bd-radius);
  text-shadow: var(--cmsmasters-button-text-shadow);
  box-shadow: var(--cmsmasters-button-normal-box-shadow);
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  display: inline-block;
  cursor: pointer;
  word-break: normal;
  position: relative;
  z-index: 0;
  transition: all 0.3s ease-in-out;
  overflow: visible;
  background-color: var(--cmsmasters-button-normal-colors-bg);
  background-image: var(--cmsmasters-button-normal-colors-bg-image);
}
.woocommerce input.button:hover,
.woocommerce #respond input#submit:hover {
  color: var(--cmsmasters-button-hover-colors-color);
  border-color: var(--cmsmasters-button-hover-colors-bd);
  border-radius: var(--cmsmasters-button-hover-bd-radius);
  text-decoration: var(--cmsmasters-button-hover-text-decoration);
  text-shadow: var(--cmsmasters-button-hover-text-shadow);
  box-shadow: var(--cmsmasters-button-hover-box-shadow);
  background-color: var(--cmsmasters-button-hover-colors-bg);
  background-image: var(--cmsmasters-button-hover-colors-bg-image);
}
.woocommerce a.button,
.woocommerce button.button,
.woocommerce input.button,
.woocommerce #respond input#submit {
  text-align: center;
  position: relative;
}
.woocommerce a.button.loading,
.woocommerce button.button.loading,
.woocommerce input.button.loading,
.woocommerce #respond input#submit.loading {
  color: transparent;
  opacity: 0.8;
  pointer-events: none;
}
.woocommerce a.button.loading span:after,
.woocommerce button.button.loading span:after,
.woocommerce input.button.loading span:after,
.woocommerce #respond input#submit.loading span:after {
  content: "\e931";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.5em;
  color: var(--cmsmasters-button-normal-colors-color);
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  animation: cmsmasters-spin 2s infinite linear;
}
.woocommerce a.button.added,
.woocommerce button.button.added,
.woocommerce input.button.added,
.woocommerce #respond input#submit.added {
  outline: none;
}
.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt,
.woocommerce #respond input#submit.alt {
  -webkit-font-smoothing: antialiased;
}
.woocommerce a.button.alt.disabled, .woocommerce a.button.alt:disabled, .woocommerce a.button.alt:disabled[disabled], .woocommerce a.button.alt.disabled:hover, .woocommerce a.button.alt:disabled:hover, .woocommerce a.button.alt:disabled[disabled]:hover,
.woocommerce button.button.alt.disabled,
.woocommerce button.button.alt:disabled,
.woocommerce button.button.alt:disabled[disabled],
.woocommerce button.button.alt.disabled:hover,
.woocommerce button.button.alt:disabled:hover,
.woocommerce button.button.alt:disabled[disabled]:hover,
.woocommerce input.button.alt.disabled,
.woocommerce input.button.alt:disabled,
.woocommerce input.button.alt:disabled[disabled],
.woocommerce input.button.alt.disabled:hover,
.woocommerce input.button.alt:disabled:hover,
.woocommerce input.button.alt:disabled[disabled]:hover,
.woocommerce #respond input#submit.alt.disabled,
.woocommerce #respond input#submit.alt:disabled,
.woocommerce #respond input#submit.alt:disabled[disabled],
.woocommerce #respond input#submit.alt.disabled:hover,
.woocommerce #respond input#submit.alt:disabled:hover,
.woocommerce #respond input#submit.alt:disabled[disabled]:hover {
  opacity: 0.5;
}
.woocommerce a.button:disabled, .woocommerce a.button.disabled, .woocommerce a.button:disabled[disabled],
.woocommerce button.button:disabled,
.woocommerce button.button.disabled,
.woocommerce button.button:disabled[disabled],
.woocommerce input.button:disabled,
.woocommerce input.button.disabled,
.woocommerce input.button:disabled[disabled],
.woocommerce #respond input#submit:disabled,
.woocommerce #respond input#submit.disabled,
.woocommerce #respond input#submit:disabled[disabled] {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.5;
}
.woocommerce .cart .button,
.woocommerce .cart input.button {
  float: none;
}
.woocommerce a.added_to_cart {
  text-align: center;
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  color: var(--cmsmasters-button-normal-colors-color);
  border-color: var(--cmsmasters-button-normal-colors-bd);
  border-style: var(--cmsmasters-button-normal-bd-style);
  border-top-width: var(--cmsmasters-button-normal-bd-width-top);
  border-right-width: var(--cmsmasters-button-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-button-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-button-normal-bd-width-left);
  border-radius: var(--cmsmasters-button-normal-bd-radius);
  text-shadow: var(--cmsmasters-button-text-shadow);
  box-shadow: var(--cmsmasters-button-normal-box-shadow);
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  display: inline-block;
  cursor: pointer;
  word-break: normal;
  position: relative;
  z-index: 0;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  background-color: transparent;
  background-image: none;
  margin-top: 1em;
  position: relative;
}
.woocommerce a.added_to_cart:hover {
  color: var(--cmsmasters-button-hover-colors-color);
  border-color: var(--cmsmasters-button-hover-colors-bd);
  border-radius: var(--cmsmasters-button-hover-bd-radius);
  text-decoration: var(--cmsmasters-button-hover-text-decoration);
  text-shadow: var(--cmsmasters-button-hover-text-shadow);
  box-shadow: var(--cmsmasters-button-hover-box-shadow);
}
.woocommerce a.added_to_cart:before, .woocommerce a.added_to_cart:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
}
.woocommerce a.added_to_cart:before {
  background-color: var(--cmsmasters-button-normal-colors-bg);
  background-image: var(--cmsmasters-button-normal-colors-bg-image);
  opacity: 1;
}
.woocommerce a.added_to_cart:after {
  background-color: var(--cmsmasters-button-hover-colors-bg);
  background-image: var(--cmsmasters-button-hover-colors-bg-image);
  opacity: 0;
}
.woocommerce a.added_to_cart:hover:before {
  opacity: 0;
}
.woocommerce a.added_to_cart:hover:after {
  opacity: 1;
}
.woocommerce #reviews #comments .add_review:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce #reviews #comments h2 {
  clear: none;
}
.woocommerce #reviews #comments ol.commentlist {
  list-style: none;
  background: none;
  width: 100%;
  margin: 0;
  padding: 0;
}
.woocommerce #reviews #comments ol.commentlist:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce #reviews #comments ol.commentlist > li:first-child {
  margin-top: 0;
}
.woocommerce #reviews #comments ol.commentlist li {
  border: 0;
  background: none;
  margin: 0;
  margin-top: var(--cmsmasters-single-comments-items-vert-gap);
  padding: 0;
  position: relative;
}
.woocommerce #reviews #comments ol.commentlist li:before {
  content: none;
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .woocommerce #reviews #comments ol.commentlist li .comment_container {
    display: flex;
    align-items: flex-start;
  }
}
.woocommerce #reviews #comments ol.commentlist li .meta {
  outline: none;
}
.woocommerce #reviews #comments ol.commentlist li img.avatar {
  border-radius: 200px;
  overflow: hidden;
  flex-shrink: 0;
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .woocommerce #reviews #comments ol.commentlist li img.avatar {
    margin-bottom: 0;
    margin-left: 30px;
  }
}
.woocommerce #reviews #comments ol.commentlist li .comment-text {
  flex-grow: 1;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row-reverse;
  justify-content: space-between;
  overflow: hidden;
}
.woocommerce #reviews #comments ol.commentlist li .star-rating {
  color: var(--cmsmasters-colors-link);
  flex-shrink: 0;
}
.woocommerce #reviews #comments ol.commentlist li .meta {
  flex-grow: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  margin: 0;
  padding-left: 5px;
}
.woocommerce #reviews #comments ol.commentlist li .woocommerce-review__author {
  font-family: var(--cmsmasters-h5-font-family);
  font-weight: var(--cmsmasters-h5-font-weight);
  font-style: var(--cmsmasters-h5-font-style);
  text-transform: var(--cmsmasters-h5-text-transform);
  text-decoration: var(--cmsmasters-h5-text-decoration);
  font-size: var(--cmsmasters-h5-font-size);
  line-height: var(--cmsmasters-h5-line-height);
  letter-spacing: var(--cmsmasters-h5-letter-spacing);
  word-spacing: var(--cmsmasters-h5-word-spacing);
  margin: 0;
  margin-left: 22px;
}
.woocommerce #reviews #comments ol.commentlist li .woocommerce-review__dash {
  display: none;
}
.woocommerce #reviews #comments ol.commentlist li .woocommerce-review__published-date {
  color: var(--cmsmasters-colors-hover);
  font-family: var(--cmsmasters-accent-font-family);
  font-weight: var(--cmsmasters-accent-font-weight);
  font-style: var(--cmsmasters-accent-font-style);
  text-transform: var(--cmsmasters-accent-text-transform);
  text-decoration: var(--cmsmasters-accent-text-decoration);
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
  letter-spacing: var(--cmsmasters-accent-letter-spacing);
  word-spacing: var(--cmsmasters-accent-word-spacing);
  line-height: var(--cmsmasters-h5-line-height);
}
.woocommerce #reviews #comments ol.commentlist li .description {
  flex-basis: 100%;
  margin-top: 8px;
}
.woocommerce #reviews #comments ol.commentlist li .description > *:first-child {
  margin-top: 0;
}
.woocommerce #reviews #comments ol.commentlist li .description > *:last-child {
  margin-bottom: 0;
}
.woocommerce #reviews #comments ol.commentlist ul.children {
  list-style: none;
  margin: 0;
  margin-right: var(--cmsmasters-single-comments-items-hor-gap);
}
.woocommerce #reviews #comments ol.commentlist ul.children .star-rating {
  display: none;
}
.woocommerce .star-rating {
  overflow: hidden;
  position: relative;
  height: 1em;
  line-height: 1;
  font-size: 1em;
  width: 5em;
  font-family: eicons;
}
.woocommerce .star-rating::before {
  content: "\e933\e933\e933\e933\e933";
  color: var(--cmsmasters-colors-hover);
  top: 0;
  left: 0;
  position: absolute;
}
.woocommerce .star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1em;
}
.woocommerce .star-rating span::before {
  content: "\e934\e934\e934\e934\e934";
  top: 0;
  position: absolute;
  left: 0;
}
.woocommerce .woocommerce-product-rating {
  line-height: 2;
  display: block;
}
.woocommerce .woocommerce-product-rating:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce .woocommerce-product-rating .star-rating {
  margin: 0.5em 4px 0 0;
  float: left;
}
.woocommerce .products .star-rating {
  display: block;
  margin: 0 0 0.5em;
  float: none;
}
.woocommerce .hreview-aggregate .star-rating {
  margin: 10px 0 0;
}
.woocommerce .woocommerce-Reviews #review_form #respond:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce .woocommerce-Reviews #review_form #respond .form-submit input {
  left: auto;
}
.woocommerce .woocommerce-Reviews #review_form #respond textarea {
  height: 80px;
  width: 100%;
}
.woocommerce .woocommerce-Reviews .comment-form > *:first-child {
  margin-top: 0;
}
.woocommerce .woocommerce-Reviews .comment-form > *:last-child {
  margin-bottom: 0;
}
.woocommerce .comment-form-rating {
  margin: 0 0 1.5rem;
}
.woocommerce p.stars {
  margin: 0;
}
.woocommerce p.stars span {
  display: flex;
  overflow: hidden;
  justify-content: flex-start;
}
.woocommerce p.stars a {
  text-decoration: none;
  text-indent: -999em;
  height: 1em;
  width: 1em;
  position: relative;
}
.woocommerce p.stars a::before {
  content: "\e933";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: block;
  width: 1em;
  height: 1em;
  position: absolute;
  left: 0;
  top: 0;
  text-indent: 0;
}
.woocommerce p.stars a:hover ~ a::before {
  content: "\e933";
}
.woocommerce p.stars:hover a::before {
  content: "\e934";
}
.woocommerce p.stars.selected a.active::before {
  content: "\e934";
}
.woocommerce p.stars.selected a.active ~ a::before {
  content: "\e933";
}
.woocommerce p.stars.selected a:not(.active)::before {
  content: "\e934";
}
.woocommerce table.shop_attributes th {
  font-weight: 700;
  width: 150px;
}
.woocommerce table.shop_attributes p:first-child {
  margin-top: 0;
}
.woocommerce table.shop_attributes p:last-child {
  margin-bottom: 0;
}
.woocommerce table.shop_table {
  margin-top: 0;
}
.woocommerce table.shop_table th {
  outline: none;
}
.woocommerce table.shop_table td small {
  font-weight: normal;
}
.woocommerce table.shop_table td del {
  font-weight: normal;
}
@media only screen and (max-width: 768px) {
  .woocommerce table.shop_table_responsive {
    display: block;
  }
  .woocommerce table.shop_table_responsive tbody {
    display: block;
  }
  .woocommerce table.shop_table_responsive tbody tr td {
    border-width: 0 1px 1px 1px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  .woocommerce table.shop_table_responsive tbody tr td:first-child {
    border-top-width: 1px;
  }
  .woocommerce table.shop_table_responsive tbody tr td.product-remove {
    border-bottom-width: 0;
  }
  .woocommerce table.shop_table_responsive tbody tr:nth-child(2n) td {
    background-color: var(--cmsmasters-table-colors-bg);
  }
  .woocommerce table.shop_table_responsive tbody tr:first-child td:first-child {
    border-top-width: 1px;
    border-top-style: solid;
    border-top-color: var(--cmsmasters-table-colors-bd);
  }
  .woocommerce table.shop_table_responsive tbody tr input.qty {
    text-align: left;
  }
  .woocommerce table.shop_table_responsive.cart tbody tr + tr {
    margin-top: 25px;
  }
}
.woocommerce table.my_account_orders th,
.woocommerce table.my_account_orders td,
.woocommerce table.order_details th,
.woocommerce table.order_details td {
  padding: 10px 15px;
  vertical-align: middle;
}
.woocommerce table.my_account_orders .button,
.woocommerce table.order_details .button {
  white-space: nowrap;
}
.woocommerce table.woocommerce-MyAccount-downloads td,
.woocommerce table.woocommerce-MyAccount-downloads th {
  vertical-align: top;
  text-align: center;
}
.woocommerce table.woocommerce-MyAccount-downloads td:first-child,
.woocommerce table.woocommerce-MyAccount-downloads th:first-child {
  text-align: left;
}
.woocommerce table.woocommerce-MyAccount-downloads td:last-child,
.woocommerce table.woocommerce-MyAccount-downloads th:last-child {
  text-align: left;
}
.woocommerce table.woocommerce-MyAccount-downloads td .woocommerce-MyAccount-downloads-file::before,
.woocommerce table.woocommerce-MyAccount-downloads th .woocommerce-MyAccount-downloads-file::before {
  content: "\e880";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.woocommerce td.product-name .wc-item-meta {
  list-style: none outside;
  overflow: hidden;
}
.woocommerce td.product-name .wc-item-meta .wc-item-meta-label {
  float: left;
  clear: both;
  margin-right: 0.25em;
  list-style: none outside;
}
.woocommerce td.product-name .wc-item-meta p, .woocommerce td.product-name .wc-item-meta:last-child {
  margin-bottom: 0;
}
.woocommerce td.product-name p.backorder_notification {
  font-size: 0.83em;
}
.woocommerce th.product-quantity {
  white-space: nowrap;
}
@media only screen and (min-width: 769px) {
  .woocommerce td.product-quantity {
    width: 80px;
  }
  .woocommerce td.product-quantity .quantity .qty {
    min-width: 100%;
  }
}
.woocommerce ul.cart_list,
.woocommerce ul.product_list_widget {
  list-style: none outside;
  padding: 0;
  margin: 0;
}
.woocommerce ul.cart_list li,
.woocommerce ul.product_list_widget li {
  min-height: 50px;
  padding: 0 65px 0 0;
  margin: 15px 0 0;
  position: relative;
}
.woocommerce ul.cart_list li:after,
.woocommerce ul.product_list_widget li:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce ul.cart_list li:first-child,
.woocommerce ul.product_list_widget li:first-child {
  margin-top: 0;
}
.woocommerce ul.cart_list li a:not(.remove),
.woocommerce ul.product_list_widget li a:not(.remove) {
  font-family: var(--cmsmasters-accent-font-family);
  font-weight: var(--cmsmasters-accent-font-weight);
  font-style: var(--cmsmasters-accent-font-style);
  text-transform: var(--cmsmasters-accent-text-transform);
  text-decoration: var(--cmsmasters-accent-text-decoration);
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
  letter-spacing: var(--cmsmasters-accent-letter-spacing);
  word-spacing: var(--cmsmasters-accent-word-spacing);
  color: var(--cmsmasters-colors-heading);
  display: block;
}
.woocommerce ul.cart_list li a:not(.remove):hover,
.woocommerce ul.product_list_widget li a:not(.remove):hover {
  color: var(--cmsmasters-colors-link);
}
.woocommerce ul.cart_list li .quantity,
.woocommerce ul.product_list_widget li .quantity {
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
}
.woocommerce ul.cart_list li .amount,
.woocommerce ul.product_list_widget li .amount {
  color: var(--cmsmasters-colors-link);
}
.woocommerce ul.cart_list li del + ins,
.woocommerce ul.product_list_widget li del + ins {
  margin-right: 5px;
}
.woocommerce ul.cart_list li img,
.woocommerce ul.product_list_widget li img {
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  width: 50px;
  height: 50px;
  margin: auto !important;
  position: absolute;
  left: auto;
  right: 0;
  top: 0;
  bottom: auto;
  box-shadow: none;
}
.woocommerce ul.cart_list li .star-rating,
.woocommerce ul.product_list_widget li .star-rating {
  color: var(--cmsmasters-colors-link);
  float: none;
  margin-top: 5px;
}
.woocommerce ul.cart_list li .reviewer,
.woocommerce ul.product_list_widget li .reviewer {
  font-family: var(--cmsmasters-accent-font-family);
  font-weight: var(--cmsmasters-accent-font-weight);
  font-style: var(--cmsmasters-accent-font-style);
  text-transform: var(--cmsmasters-accent-text-transform);
  text-decoration: var(--cmsmasters-accent-text-decoration);
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
  letter-spacing: var(--cmsmasters-accent-letter-spacing);
  word-spacing: var(--cmsmasters-accent-word-spacing);
  margin-top: 5px;
  display: inline-block;
}
.woocommerce.widget_shopping_cart .total,
.woocommerce .widget_shopping_cart .total {
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: var(--cmsmasters-colors-bd);
  padding: 15px 0 0;
  margin: 20px 0 0;
}
.woocommerce.widget_shopping_cart .total strong,
.woocommerce .widget_shopping_cart .total strong {
  min-width: 40px;
  display: inline-block;
}
.woocommerce.widget_shopping_cart .cart_list li,
.woocommerce .widget_shopping_cart .cart_list li {
  padding-left: 2em;
  position: relative;
}
.woocommerce.widget_shopping_cart .cart_list li a.remove,
.woocommerce .widget_shopping_cart .cart_list li a.remove {
  margin: auto;
  position: absolute;
  top: 0;
  right: 0;
  bottom: auto;
  left: auto;
}
.woocommerce.widget_shopping_cart .buttons,
.woocommerce .widget_shopping_cart .buttons {
  display: flex;
  justify-content: space-between;
  margin: 15px 0 0;
}
.woocommerce.widget_shopping_cart .buttons a,
.woocommerce .widget_shopping_cart .buttons a {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  padding-top: calc(var(--cmsmasters-button-padding-top)/2);
  padding-right: calc(var(--cmsmasters-button-padding-right)/2);
  padding-bottom: calc(var(--cmsmasters-button-padding-bottom)/2);
  padding-left: calc(var(--cmsmasters-button-padding-left)/2);
}
.woocommerce.widget_shopping_cart .buttons a + a,
.woocommerce .widget_shopping_cart .buttons a + a {
  margin-right: 4px;
}
.woocommerce form .form-row [placeholder]:focus::-webkit-input-placeholder {
  transition: opacity 0.5s 0.5s ease;
  opacity: 0;
}
.woocommerce form .form-row label {
  line-height: 2;
}
.woocommerce form .form-row label.hidden {
  visibility: hidden;
}
.woocommerce form .form-row label.inline {
  display: inline;
}
.woocommerce form .form-row .woocommerce-input-wrapper .description {
  background: #1e85be;
  color: #fff;
  border-radius: 3px;
  padding: 1em;
  margin: 0.5em 0 0;
  clear: both;
  display: none;
  position: relative;
}
.woocommerce form .form-row .woocommerce-input-wrapper .description a {
  color: #fff;
  text-decoration: underline;
  border: 0;
  box-shadow: none;
}
.woocommerce form .form-row .woocommerce-input-wrapper .description::before {
  left: 50%;
  top: 0%;
  margin-top: -4px;
  transform: translateX(-50%) rotate(180deg);
  content: "";
  position: absolute;
  border-width: 4px 6px 0 6px;
  border-style: solid;
  border-color: #1e85be transparent transparent transparent;
  z-index: 100;
  display: block;
}
.woocommerce form .form-row select {
  cursor: pointer;
  margin: 0;
}
.woocommerce form .form-row .required {
  color: red;
  font-weight: 700;
  border: 0 !important;
  text-decoration: none;
  visibility: hidden;
}
.woocommerce form .form-row .optional {
  visibility: visible;
}
.woocommerce form .form-row .input-checkbox {
  display: inline;
  margin: -2px 8px 0 0;
  text-align: center;
  vertical-align: middle;
}
.woocommerce form .form-row input.input-text,
.woocommerce form .form-row textarea {
  width: 100%;
  margin: 0;
  outline: 0;
}
.woocommerce form .form-row textarea {
  display: block;
}
.woocommerce form .form-row .select2-container {
  width: 100%;
  line-height: 2em;
}
.woocommerce form .form-row.woocommerce-invalid label {
  color: #aa0000;
}
.woocommerce form .form-row.woocommerce-invalid .select2-container,
.woocommerce form .form-row.woocommerce-invalid input.input-text,
.woocommerce form .form-row.woocommerce-invalid select {
  border-color: #aa0000;
}
.woocommerce form .form-row.woocommerce-validated .select2-container,
.woocommerce form .form-row.woocommerce-validated input.input-text,
.woocommerce form .form-row.woocommerce-validated select {
  border-color: #7ad03a;
}
.woocommerce form .form-row ::-webkit-input-placeholder {
  line-height: normal;
}
.woocommerce form .form-row :-moz-placeholder {
  line-height: normal;
}
.woocommerce form .form-row :-ms-input-placeholder {
  line-height: normal;
}
.woocommerce form.checkout_coupon,
.woocommerce form.register {
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  padding: 20px;
  margin: 3rem 0;
}
.woocommerce form.checkout_coupon {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.woocommerce form.checkout_coupon p {
  margin: 1.5rem 0 0;
  display: flex;
}
.woocommerce form.checkout_coupon p:first-child {
  flex-basis: 100%;
  margin-top: 0;
}
.woocommerce .woocommerce-form-coupon-toggle + .checkout_coupon {
  margin-top: calc(3rem * -1 + 10px);
}
.woocommerce ul#shipping_method {
  list-style: none outside;
  margin: 0;
  padding: 0;
}
.woocommerce ul#shipping_method li {
  list-style: none outside;
  margin: 0.5em 0 0;
  padding: 0;
}
.woocommerce ul#shipping_method li:first-child {
  margin-top: 0;
}
.woocommerce ul#shipping_method .amount {
  font-weight: 700;
}
.woocommerce p.woocommerce-shipping-contents {
  margin: 0;
}
.woocommerce ul.order_details {
  list-style: none;
  margin: 0 0 3rem;
}
.woocommerce ul.order_details:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce ul.order_details li {
  border-top-width: 1px;
  border-top-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  margin: 10px 0 0;
  padding: 10px 0 0;
}
.woocommerce ul.order_details li strong {
  display: block;
}
.woocommerce ul.order_details li:first-child {
  border-top-width: 0;
  border-top-style: solid;
  margin: 0;
  padding: 0;
}
.woocommerce .wc-bacs-bank-details-heading,
.woocommerce .woocommerce-order-details__title,
.woocommerce .woocommerce-column__title {
  font-family: var(--cmsmasters-h3-font-family);
  font-weight: var(--cmsmasters-h3-font-weight);
  font-style: var(--cmsmasters-h3-font-style);
  text-transform: var(--cmsmasters-h3-text-transform);
  text-decoration: var(--cmsmasters-h3-text-decoration);
  font-size: var(--cmsmasters-h3-font-size);
  line-height: var(--cmsmasters-h3-line-height);
  letter-spacing: var(--cmsmasters-h3-letter-spacing);
  word-spacing: var(--cmsmasters-h3-word-spacing);
}
.woocommerce .wc-bacs-bank-details-account-name {
  font-family: var(--cmsmasters-h4-font-family);
  font-weight: var(--cmsmasters-h4-font-weight);
  font-style: var(--cmsmasters-h4-font-style);
  text-transform: var(--cmsmasters-h4-text-transform);
  text-decoration: var(--cmsmasters-h4-text-decoration);
  font-size: var(--cmsmasters-h4-font-size);
  line-height: var(--cmsmasters-h4-line-height);
  letter-spacing: var(--cmsmasters-h4-letter-spacing);
  word-spacing: var(--cmsmasters-h4-word-spacing);
  margin-top: 0;
}
.woocommerce .woocommerce-order-downloads,
.woocommerce .woocommerce-customer-details,
.woocommerce .woocommerce-order-details {
  margin-bottom: 3rem;
}
.woocommerce .woocommerce-order-downloads:last-child,
.woocommerce .woocommerce-order-downloads *:last-child,
.woocommerce .woocommerce-customer-details:last-child,
.woocommerce .woocommerce-customer-details *:last-child,
.woocommerce .woocommerce-order-details:last-child,
.woocommerce .woocommerce-order-details *:last-child {
  margin-bottom: 0;
}
.woocommerce .woocommerce-order-downloads .order-again,
.woocommerce .woocommerce-customer-details .order-again,
.woocommerce .woocommerce-order-details .order-again {
  margin-top: 1.5rem;
}
.woocommerce .woocommerce-customer-details address {
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  width: 100%;
  font-style: normal;
  margin: 0;
  padding: 10px 20px;
}
.woocommerce .woocommerce-customer-details .woocommerce-customer-details--phone,
.woocommerce .woocommerce-customer-details .woocommerce-customer-details--email {
  margin-bottom: 0;
  padding-left: 1.5em;
  position: relative;
}
.woocommerce .woocommerce-customer-details .woocommerce-customer-details--phone:before,
.woocommerce .woocommerce-customer-details .woocommerce-customer-details--email:before {
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: auto;
  right: 0;
  top: 0;
  bottom: 0;
}
.woocommerce .woocommerce-customer-details .woocommerce-customer-details--phone::before {
  content: "\e887";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.woocommerce .woocommerce-customer-details .woocommerce-customer-details--email::before {
  content: "\e894";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.woocommerce .widget_layered_nav > ul {
  list-style: none;
  margin-right: 0;
  padding-right: 0;
  color: var(--cmsmasters-colors-hover);
  margin-top: 0;
  margin-bottom: 0;
}
.woocommerce .widget_layered_nav > ul li {
  margin-top: 10px;
}
.woocommerce .widget_layered_nav > ul > li:first-child {
  margin-top: 0;
}
.woocommerce .widget_layered_nav > ul a {
  color: var(--cmsmasters-colors-heading);
  margin-left: 6px;
}
.woocommerce .widget_layered_nav > ul a:hover {
  color: var(--cmsmasters-colors-link);
}
.woocommerce .widget_layered_nav > ul ul {
  list-style: none;
}
.woocommerce .widget_layered_nav .woocommerce-widget-layered-nav-list__item--chosen a:before {
  content: "\e87f";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  text-decoration: none;
  color: var(--cmsmasters-button-normal-colors-color);
  background-color: var(--cmsmasters-button-normal-colors-bg);
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-button-normal-colors-bd);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.4em;
  height: 1.4em;
  margin-left: 10px;
  vertical-align: middle;
  position: relative;
  transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}
.woocommerce .widget_layered_nav .woocommerce-widget-layered-nav-list__item--chosen a:hover:before {
  color: var(--cmsmasters-button-hover-colors-color);
  background-color: var(--cmsmasters-button-hover-colors-bg);
  border-color: var(--cmsmasters-button-hover-colors-bd);
}
.woocommerce .widget_layered_nav .woocommerce-widget-layered-nav-list__item--chosen a:before {
  top: -1px;
}
.woocommerce .woocommerce-widget-layered-nav-dropdown__submit {
  margin-top: 1em;
}
.woocommerce .widget_layered_nav_filters ul {
  margin: 0;
  padding: 0;
  border: 0;
  list-style: none outside;
  overflow: hidden;
  zoom: 1;
}
.woocommerce .widget_layered_nav_filters ul li {
  float: left;
  padding: 0 1em 1px 1px;
  list-style: none;
}
.woocommerce .widget_layered_nav_filters ul li a {
  text-decoration: none;
}
.woocommerce .widget_layered_nav_filters ul li a::before {
  content: "\e93a";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #aa0000;
  vertical-align: inherit;
  margin-right: 0.5em;
}
.woocommerce .widget_price_filter .price_slider {
  margin-bottom: 1.5rem;
}
.woocommerce .widget_price_filter .price_slider_amount {
  display: flex;
  align-items: center;
}
.woocommerce .widget_price_filter .price_slider_amount .button {
  margin-left: 10px;
}
.woocommerce .widget_price_filter .ui-slider {
  position: relative;
  text-align: left;
  margin-left: 0.5em;
  margin-right: 0.5em;
}
.woocommerce .widget_price_filter .ui-slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 1em;
  height: 1em;
  background-color: var(--cmsmasters-colors-link);
  border-radius: 1em;
  cursor: ew-resize;
  outline: none;
  top: -0.3em;
  /* rtl:ignore */
  margin-left: -0.5em;
}
.woocommerce .widget_price_filter .ui-slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  font-size: 0.7em;
  display: block;
  border: 0;
  border-radius: 1em;
  background-color: var(--cmsmasters-colors-link);
}
.woocommerce .widget_price_filter .price_slider_wrapper .ui-widget-content {
  border-radius: 1em;
  background-color: var(--cmsmasters-colors-heading);
  border: 0;
}
.woocommerce .widget_price_filter .ui-slider-horizontal {
  height: 0.5em;
}
.woocommerce .widget_price_filter .ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%;
}
.woocommerce .widget_price_filter .ui-slider-horizontal .ui-slider-range-min {
  left: -1px;
}
.woocommerce .widget_price_filter .ui-slider-horizontal .ui-slider-range-max {
  right: -1px;
}
.woocommerce .widget_price_filter .price_label {
  font-size: var(--cmsmasters-h6-font-size);
}
.woocommerce .widget_rating_filter ul {
  margin: 0;
  padding: 0;
  border: 0;
  list-style: none outside;
}
.woocommerce .widget_rating_filter ul li {
  margin: 10px 0 0;
  padding: 0;
  list-style: none;
}
.woocommerce .widget_rating_filter ul li:first-child {
  margin-top: 0;
}
.woocommerce .widget_rating_filter ul li a {
  vertical-align: middle;
  text-decoration: none;
}
.woocommerce .widget_rating_filter ul li .star-rating {
  float: none;
  display: inline-block;
  vertical-align: middle;
}
.woocommerce .widget_rating_filter ul li.chosen a:before {
  content: "\e87f";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  text-decoration: none;
  color: var(--cmsmasters-button-normal-colors-color);
  background-color: var(--cmsmasters-button-normal-colors-bg);
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-button-normal-colors-bd);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.4em;
  height: 1.4em;
  margin-left: 10px;
  vertical-align: middle;
  position: relative;
  transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}
.woocommerce .widget_rating_filter ul li.chosen a:hover:before {
  color: var(--cmsmasters-button-hover-colors-color);
  background-color: var(--cmsmasters-button-hover-colors-bg);
  border-color: var(--cmsmasters-button-hover-colors-bd);
}
.woocommerce .woocommerce-product-search {
  position: relative;
}
.woocommerce .woocommerce-product-search input[type=search], .woocommerce .woocommerce-product-search input[type=text] {
  width: 100%;
  margin: 0;
  padding-left: calc(1em + 5px + var(--cmsmasters-input-padding-left));
}
.woocommerce .woocommerce-product-search button {
  font-size: var(--cmsmasters-input-font-size);
  color: var(--cmsmasters-input-normal-colors-color);
  background-color: transparent;
  background-image: none;
  border: 0;
  width: 1em;
  height: 100%;
  margin: auto !important;
  position: absolute;
  left: var(--cmsmasters-input-padding-left);
  right: auto;
  top: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
  border-radius: 0;
  text-shadow: none;
  box-shadow: none;
  overflow: hidden;
  word-break: normal;
  transition: all 0.3s ease-in-out;
}
.woocommerce .woocommerce-product-search button:hover {
  color: var(--cmsmasters-input-focus-colors-color);
}
.woocommerce .woocommerce-product-search button:before, .woocommerce .woocommerce-product-search button:after {
  content: none;
  display: none;
}
.woocommerce .woocommerce-product-search button i,
.woocommerce .woocommerce-product-search button i:before {
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.widget_product_categories > ul {
  list-style: none;
  margin-right: 0;
  padding-right: 0;
  color: var(--cmsmasters-colors-hover);
  margin-top: 0;
  margin-bottom: 0;
}
.widget_product_categories > ul li {
  margin-top: 10px;
}
.widget_product_categories > ul > li:first-child {
  margin-top: 0;
}
.widget_product_categories > ul a {
  color: var(--cmsmasters-colors-heading);
  margin-left: 6px;
}
.widget_product_categories > ul a:hover {
  color: var(--cmsmasters-colors-link);
}
.widget_product_categories > ul ul {
  list-style: none;
}

@media only screen and (max-width: 540px) {
  .woocommerce ul.products[class*=columns-] li.product, .woocommerce ul.products[class*=columns-] li.product:nth-child(2n),
  .woocommerce-page ul.products[class*=columns-] li.product,
  .woocommerce-page ul.products[class*=columns-] li.product:nth-child(2n) {
    width: 100%;
    float: none;
    clear: both;
  }
}
.woocommerce form .show-password-input,
.woocommerce-page form .show-password-input {
  font-size: var(--cmsmasters-input-font-size);
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: 15px;
  right: auto;
  top: 0;
  bottom: auto;
  height: calc(
			var(--cmsmasters-input-line-height) +
			var(--cmsmasters-input-padding-top) +
			var(--cmsmasters-input-padding-bottom) +
			var(--cmsmasters-input-normal-bd-width-top) +
			var(--cmsmasters-input-normal-bd-width-bottom)
		);
  transition: height 0.3s ease-in-out;
}
.woocommerce form .show-password-input:after,
.woocommerce-page form .show-password-input:after {
  content: "\e8ac";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.woocommerce form .show-password-input.display-password:after,
.woocommerce-page form .show-password-input.display-password:after {
  color: var(--cmsmasters-colors-link);
}
.woocommerce form .password-input input:focus ~ .show-password-input,
.woocommerce-page form .password-input input:focus ~ .show-password-input {
  height: calc(
			var(--cmsmasters-input-line-height) +
			var(--cmsmasters-input-padding-top) +
			var(--cmsmasters-input-padding-bottom) +
			var(--cmsmasters-input-focus-bd-width-top) +
			var(--cmsmasters-input-focus-bd-width-bottom)
		);
}

.woocommerce-no-js form.woocommerce-form-login,
.woocommerce-no-js form.woocommerce-form-coupon {
  display: block !important;
}
.woocommerce-no-js .woocommerce-form-login-toggle,
.woocommerce-no-js .woocommerce-form-coupon-toggle,
.woocommerce-no-js .showcoupon {
  display: none !important;
}

.woocommerce-message,
.woocommerce-error,
.woocommerce-info {
  padding: 1em 2em 1em 3.5em;
  margin: 0 0 3rem;
  position: relative;
  background-color: var(--cmsmasters-colors-alternate);
  color: var(--cmsmasters-colors-text);
  border-top-width: 3px;
  border-top-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  border-top-color: var(--cmsmasters-colors-link);
  list-style: none outside;
  width: auto;
  word-wrap: break-word;
}
.woocommerce-message::before,
.woocommerce-error::before,
.woocommerce-info::before {
  content: "\e819";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.5em;
  color: var(--cmsmasters-colors-link);
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: auto;
  right: 0.75em;
  top: 0;
  bottom: 0;
}
.woocommerce-message li,
.woocommerce-error li,
.woocommerce-info li {
  list-style: none outside !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}
.woocommerce-message > li:first-child,
.woocommerce-error > li:first-child,
.woocommerce-info > li:first-child {
  margin-top: 0;
}

.woocommerce-message::before {
  content: "\e90d";
}

.woocommerce-error::before {
  content: "\e900";
}

.cmsmasters-wc-add-to-cart-message {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
}

.select2-dropdown {
  color: var(--cmsmasters-input-focus-colors-color);
  background-color: var(--cmsmasters-input-focus-colors-bg);
  border-color: var(--cmsmasters-input-focus-colors-bd);
  border-radius: var(--cmsmasters-input-focus-bd-radius);
  box-shadow: var(--cmsmasters-input-focus-box-shadow);
  overflow: hidden;
}
.select2-dropdown .select2-search {
  padding: 0;
}
.select2-dropdown .select2-search .select2-search__field {
  border-top: 0;
  border-right: 0;
  border-left: 0;
  border-bottom-width: var(--cmsmasters-input-normal-bd-width-bottom);
  border-color: var(--cmsmasters-input-focus-colors-bd);
  border-style: var(--cmsmasters-input-normal-bd-style);
  background: transparent;
  padding-top: var(--cmsmasters-input-padding-top);
  padding-right: var(--cmsmasters-input-padding-right);
  padding-bottom: var(--cmsmasters-input-padding-bottom);
  padding-left: var(--cmsmasters-input-padding-left);
  box-shadow: none;
  border-radius: 0;
}
.select2-dropdown .select2-results__option {
  margin: 0;
}

/**
 * Right to left styles
 */
.rtl.woocommerce .price_label,
.rtl.woocommerce .price_label span {
  /* rtl:ignore */
  direction: ltr;
  unicode-bidi: embed;
}

/**
 * Account page
 */
.woocommerce-account .woocommerce-MyAccount-navigation ul {
  list-style: none;
  margin: 0;
}
.woocommerce-account .woocommerce-MyAccount-navigation li {
  margin: 0;
}
.woocommerce-account .woocommerce-MyAccount-navigation li:not(:first-child) {
  margin-top: 10px;
}
.woocommerce-account .woocommerce-MyAccount-navigation li a {
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  text-decoration: none;
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  background-color: var(--cmsmasters-colors-alternate);
  color: var(--cmsmasters-colors-heading);
  display: block;
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out;
}
.woocommerce-account .woocommerce-MyAccount-navigation li a:hover {
  text-decoration: none;
  color: var(--cmsmasters-colors-link);
}
.woocommerce-account .woocommerce-MyAccount-navigation li.is-active a {
  background-color: var(--cmsmasters-colors-bg);
  color: var(--cmsmasters-colors-link);
}
.woocommerce-account .woocommerce-MyAccount-content {
  overflow-x: auto;
}
.woocommerce-account .woocommerce-MyAccount-content table {
  margin: 0;
}
.woocommerce-account .woocommerce-MyAccount-content > .woocommerce-info {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: space-between;
}
.woocommerce-account .woocommerce:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce-account .addresses .title {
  display: flex;
  align-items: flex-end;
  margin-bottom: 0.75rem;
}
.woocommerce-account .addresses .title h3 {
  font-family: var(--cmsmasters-h5-font-family);
  font-weight: var(--cmsmasters-h5-font-weight);
  font-style: var(--cmsmasters-h5-font-style);
  text-transform: var(--cmsmasters-h5-text-transform);
  text-decoration: var(--cmsmasters-h5-text-decoration);
  font-size: var(--cmsmasters-h5-font-size);
  line-height: var(--cmsmasters-h5-line-height);
  letter-spacing: var(--cmsmasters-h5-letter-spacing);
  word-spacing: var(--cmsmasters-h5-word-spacing);
  margin: 0;
}
.woocommerce-account .addresses .title .edit {
  font-family: var(--cmsmasters-accent-font-family);
  font-weight: var(--cmsmasters-accent-font-weight);
  font-style: var(--cmsmasters-accent-font-style);
  text-transform: var(--cmsmasters-accent-text-transform);
  text-decoration: var(--cmsmasters-accent-text-decoration);
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
  letter-spacing: var(--cmsmasters-accent-letter-spacing);
  word-spacing: var(--cmsmasters-accent-word-spacing);
  margin-right: 5px;
}
.woocommerce-account .addresses .woocommerce-column__title {
  font-family: var(--cmsmasters-h5-font-family);
  font-weight: var(--cmsmasters-h5-font-weight);
  font-style: var(--cmsmasters-h5-font-style);
  text-transform: var(--cmsmasters-h5-text-transform);
  text-decoration: var(--cmsmasters-h5-text-decoration);
  font-size: var(--cmsmasters-h5-font-size);
  line-height: var(--cmsmasters-h5-line-height);
  letter-spacing: var(--cmsmasters-h5-letter-spacing);
  word-spacing: var(--cmsmasters-h5-word-spacing);
}
.woocommerce-account .addresses address {
  margin-top: 0;
}
.woocommerce-account ol.commentlist.notes li.note p.meta {
  font-weight: 700;
  margin-bottom: 0;
}
.woocommerce-account ol.commentlist.notes li.note .description p:last-child {
  margin-bottom: 0;
}
.woocommerce-account ul.digital-downloads {
  margin-left: 0;
  padding-left: 0;
}
.woocommerce-account ul.digital-downloads li {
  list-style: none;
  margin-left: 0;
  padding-left: 0;
}
.woocommerce-account ul.digital-downloads li::before {
  content: "\e8b9";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.woocommerce-account ul.digital-downloads li .count {
  float: right;
}

/**
 * Cart/checkout page
 */
.woocommerce-cart table.cart .product-remove,
.woocommerce-checkout table.cart .product-remove,
#add_payment_method table.cart .product-remove {
  padding-left: 1em;
  padding-right: 1em;
}
.woocommerce-cart table.cart .product-thumbnail,
.woocommerce-checkout table.cart .product-thumbnail,
#add_payment_method table.cart .product-thumbnail {
  min-width: 50px;
}
.woocommerce-cart table.cart img,
.woocommerce-checkout table.cart img,
#add_payment_method table.cart img {
  display: block;
  width: 50px;
  min-width: 50px;
  margin: 0 auto;
  box-shadow: none;
}
.woocommerce-cart table.cart th,
.woocommerce-cart table.cart td,
.woocommerce-checkout table.cart th,
.woocommerce-checkout table.cart td,
#add_payment_method table.cart th,
#add_payment_method table.cart td {
  vertical-align: middle;
}
.woocommerce-cart table.cart td.actions .coupon,
.woocommerce-checkout table.cart td.actions .coupon,
#add_payment_method table.cart td.actions .coupon {
  display: flex;
  width: 100%;
  float: none;
  margin-bottom: 10px;
}
@media only screen and (max-width: 768px) {
  .woocommerce-cart table.cart td.actions .coupon,
  .woocommerce-checkout table.cart td.actions .coupon,
  #add_payment_method table.cart td.actions .coupon {
    flex-direction: column;
  }
}
.woocommerce-cart table.cart td.actions .coupon .input-text,
.woocommerce-checkout table.cart td.actions .coupon .input-text,
#add_payment_method table.cart td.actions .coupon .input-text {
  width: auto;
  margin-left: 10px;
  height: auto;
}
@media only screen and (max-width: 768px) {
  .woocommerce-cart table.cart td.actions .coupon .input-text,
  .woocommerce-checkout table.cart td.actions .coupon .input-text,
  #add_payment_method table.cart td.actions .coupon .input-text {
    margin-left: 0;
    margin-bottom: 10px;
  }
}
@media only screen and (max-width: 768px) {
  .woocommerce-cart table.cart td.actions .coupon .button,
  .woocommerce-checkout table.cart td.actions .coupon .button,
  #add_payment_method table.cart td.actions .coupon .button {
    width: 100%;
    float: none;
  }
}
.woocommerce-cart table.cart td.actions > .button,
.woocommerce-checkout table.cart td.actions > .button,
#add_payment_method table.cart td.actions > .button {
  width: 100%;
}
.woocommerce-cart table.cart input,
.woocommerce-checkout table.cart input,
#add_payment_method table.cart input {
  margin: 0;
  vertical-align: middle;
}
.woocommerce-cart .wc-proceed-to-checkout,
.woocommerce-checkout .wc-proceed-to-checkout,
#add_payment_method .wc-proceed-to-checkout {
  margin: 1em 0 0;
}
.woocommerce-cart .wc-proceed-to-checkout:after,
.woocommerce-checkout .wc-proceed-to-checkout:after,
#add_payment_method .wc-proceed-to-checkout:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce-cart .wc-proceed-to-checkout a.checkout-button,
.woocommerce-checkout .wc-proceed-to-checkout a.checkout-button,
#add_payment_method .wc-proceed-to-checkout a.checkout-button {
  display: block;
}
.woocommerce-cart .cart-collaterals .shipping-calculator-button,
.woocommerce-checkout .cart-collaterals .shipping-calculator-button,
#add_payment_method .cart-collaterals .shipping-calculator-button {
  float: none;
  margin-top: 0.5em;
  display: inline-block;
}
.woocommerce-cart .cart-collaterals .shipping-calculator-button::after,
.woocommerce-checkout .cart-collaterals .shipping-calculator-button::after,
#add_payment_method .cart-collaterals .shipping-calculator-button::after {
  content: "\e929";
  font-family: eicons;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin-left: 0.618em;
}
.woocommerce-cart .cart-collaterals .shipping-calculator-form,
.woocommerce-checkout .cart-collaterals .shipping-calculator-form,
#add_payment_method .cart-collaterals .shipping-calculator-form {
  margin: 1em 0 0 0;
}
.woocommerce-cart .cart-collaterals .shipping-calculator-form > p,
.woocommerce-checkout .cart-collaterals .shipping-calculator-form > p,
#add_payment_method .cart-collaterals .shipping-calculator-form > p {
  padding: 0;
  margin: 10px 0 0;
}
.woocommerce-cart .cart-collaterals .shipping-calculator-form > p:first-child,
.woocommerce-checkout .cart-collaterals .shipping-calculator-form > p:first-child,
#add_payment_method .cart-collaterals .shipping-calculator-form > p:first-child {
  margin-top: 0;
}
.woocommerce-cart .cart-collaterals .cart_totals > h2,
.woocommerce-checkout .cart-collaterals .cart_totals > h2,
#add_payment_method .cart-collaterals .cart_totals > h2 {
  font-family: var(--cmsmasters-h4-font-family);
  font-weight: var(--cmsmasters-h4-font-weight);
  font-style: var(--cmsmasters-h4-font-style);
  text-transform: var(--cmsmasters-h4-text-transform);
  text-decoration: var(--cmsmasters-h4-text-decoration);
  font-size: var(--cmsmasters-h4-font-size);
  line-height: var(--cmsmasters-h4-line-height);
  letter-spacing: var(--cmsmasters-h4-letter-spacing);
  word-spacing: var(--cmsmasters-h4-word-spacing);
}
.woocommerce-cart .cart-collaterals .cart_totals p small,
.woocommerce-checkout .cart-collaterals .cart_totals p small,
#add_payment_method .cart-collaterals .cart_totals p small {
  color: var(--cmsmasters-colors-text);
  font-size: 0.83em;
}
.woocommerce-cart .cart-collaterals .cart_totals table,
.woocommerce-checkout .cart-collaterals .cart_totals table,
#add_payment_method .cart-collaterals .cart_totals table {
  margin: 0;
  padding: 0;
}
.woocommerce-cart .cart-collaterals .cart_totals table th,
.woocommerce-checkout .cart-collaterals .cart_totals table th,
#add_payment_method .cart-collaterals .cart_totals table th {
  width: 35%;
}
.woocommerce-cart .cart-collaterals .cart_totals table td,
.woocommerce-cart .cart-collaterals .cart_totals table th,
.woocommerce-checkout .cart-collaterals .cart_totals table td,
.woocommerce-checkout .cart-collaterals .cart_totals table th,
#add_payment_method .cart-collaterals .cart_totals table td,
#add_payment_method .cart-collaterals .cart_totals table th {
  vertical-align: top;
}
.woocommerce-cart .cart-collaterals .cart_totals table small,
.woocommerce-checkout .cart-collaterals .cart_totals table small,
#add_payment_method .cart-collaterals .cart_totals table small {
  color: var(--cmsmasters-colors-text);
}
.woocommerce-cart .cart-collaterals .cart_totals table select,
.woocommerce-checkout .cart-collaterals .cart_totals table select,
#add_payment_method .cart-collaterals .cart_totals table select {
  width: 100%;
}
.woocommerce-cart .cart-collaterals .cart_totals .discount td,
.woocommerce-checkout .cart-collaterals .cart_totals .discount td,
#add_payment_method .cart-collaterals .cart_totals .discount td {
  color: var(--cmsmasters-colors-link);
}
.woocommerce-cart .cart-collaterals .cart_totals .woocommerce-shipping-destination,
.woocommerce-checkout .cart-collaterals .cart_totals .woocommerce-shipping-destination,
#add_payment_method .cart-collaterals .cart_totals .woocommerce-shipping-destination {
  margin-bottom: 0;
}
.woocommerce-cart .cart-collaterals .cross-sells,
.woocommerce-checkout .cart-collaterals .cross-sells,
#add_payment_method .cart-collaterals .cross-sells {
  width: 100%;
  margin-bottom: 3rem;
}
.woocommerce-cart .cart-collaterals .cross-sells ul.products li.product,
.woocommerce-checkout .cart-collaterals .cross-sells ul.products li.product,
#add_payment_method .cart-collaterals .cross-sells ul.products li.product {
  margin-top: 0;
}
.woocommerce-cart .checkout .col-2 h3#ship-to-different-address,
.woocommerce-checkout .checkout .col-2 h3#ship-to-different-address,
#add_payment_method .checkout .col-2 h3#ship-to-different-address {
  float: left;
  clear: none;
}
.woocommerce-cart .checkout .col-2 h3#ship-to-different-address label,
.woocommerce-checkout .checkout .col-2 h3#ship-to-different-address label,
#add_payment_method .checkout .col-2 h3#ship-to-different-address label {
  font-family: inherit;
  font-weight: inherit;
  font-style: inherit;
  text-transform: inherit;
  font-size: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  word-spacing: inherit;
}
.woocommerce-cart .checkout .col-2 h3#ship-to-different-address label > *,
.woocommerce-checkout .checkout .col-2 h3#ship-to-different-address label > *,
#add_payment_method .checkout .col-2 h3#ship-to-different-address label > * {
  vertical-align: middle;
}
.woocommerce-cart .checkout .col-2 .notes,
.woocommerce-checkout .checkout .col-2 .notes,
#add_payment_method .checkout .col-2 .notes {
  clear: left;
}
.woocommerce-cart .checkout .col-2 .form-row-first,
.woocommerce-checkout .checkout .col-2 .form-row-first,
#add_payment_method .checkout .col-2 .form-row-first {
  clear: left;
}
.woocommerce-cart .checkout .create-account small,
.woocommerce-checkout .checkout .create-account small,
#add_payment_method .checkout .create-account small {
  font-size: 11px;
  color: var(--cmsmasters-colors-text);
  font-weight: normal;
}
.woocommerce-cart .checkout div.shipping-address,
.woocommerce-checkout .checkout div.shipping-address,
#add_payment_method .checkout div.shipping-address {
  padding: 0;
  clear: left;
  width: 100%;
}
.woocommerce-cart .checkout .shipping_address,
.woocommerce-checkout .checkout .shipping_address,
#add_payment_method .checkout .shipping_address {
  clear: both;
}
.woocommerce-cart .checkout .woocommerce-checkout-review-order table,
.woocommerce-checkout .checkout .woocommerce-checkout-review-order table,
#add_payment_method .checkout .woocommerce-checkout-review-order table {
  margin: 0;
}
.woocommerce-cart #payment,
.woocommerce-checkout #payment,
#add_payment_method #payment {
  background-color: var(--cmsmasters-colors-alternate);
  border-width: 1px;
  border-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  margin-top: 3rem;
}
.woocommerce-cart #payment ul.payment_methods,
.woocommerce-checkout #payment ul.payment_methods,
#add_payment_method #payment ul.payment_methods {
  list-style: none outside;
  text-align: left;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-color: var(--cmsmasters-colors-bd);
  padding: 1rem;
  margin: 0;
}
.woocommerce-cart #payment ul.payment_methods:after,
.woocommerce-checkout #payment ul.payment_methods:after,
#add_payment_method #payment ul.payment_methods:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce-cart #payment ul.payment_methods li,
.woocommerce-checkout #payment ul.payment_methods li,
#add_payment_method #payment ul.payment_methods li {
  margin: 0;
}
.woocommerce-cart #payment ul.payment_methods li:not(.woocommerce-notice),
.woocommerce-checkout #payment ul.payment_methods li:not(.woocommerce-notice),
#add_payment_method #payment ul.payment_methods li:not(.woocommerce-notice) {
  padding: 0;
}
.woocommerce-cart #payment ul.payment_methods li img,
.woocommerce-checkout #payment ul.payment_methods li img,
#add_payment_method #payment ul.payment_methods li img {
  vertical-align: middle;
  margin: -2px 0 0 0.5em;
  padding: 0;
  position: relative;
  box-shadow: none;
}
.woocommerce-cart #payment ul.payment_methods li img + img,
.woocommerce-checkout #payment ul.payment_methods li img + img,
#add_payment_method #payment ul.payment_methods li img + img {
  margin-right: 2px;
}
.woocommerce-cart #payment ul.payment_methods li:not(.woocommerce-notice):after,
.woocommerce-checkout #payment ul.payment_methods li:not(.woocommerce-notice):after,
#add_payment_method #payment ul.payment_methods li:not(.woocommerce-notice):after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce-cart #payment div.payment_box,
.woocommerce-checkout #payment div.payment_box,
#add_payment_method #payment div.payment_box {
  position: relative;
  width: 100%;
  padding: 1em;
  margin: 1em 0;
  background-color: var(--cmsmasters-colors-bg);
  color: var(--cmsmasters-colors-text);
}
.woocommerce-cart #payment div.payment_box input.input-text,
.woocommerce-cart #payment div.payment_box textarea,
.woocommerce-checkout #payment div.payment_box input.input-text,
.woocommerce-checkout #payment div.payment_box textarea,
#add_payment_method #payment div.payment_box input.input-text,
#add_payment_method #payment div.payment_box textarea {
  border-color: var(--cmsmasters-colors-bd);
}
.woocommerce-cart #payment div.payment_box ::-webkit-input-placeholder,
.woocommerce-checkout #payment div.payment_box ::-webkit-input-placeholder,
#add_payment_method #payment div.payment_box ::-webkit-input-placeholder {
  color: var(--cmsmasters-colors-text);
}
.woocommerce-cart #payment div.payment_box :-moz-placeholder,
.woocommerce-checkout #payment div.payment_box :-moz-placeholder,
#add_payment_method #payment div.payment_box :-moz-placeholder {
  color: var(--cmsmasters-colors-text);
}
.woocommerce-cart #payment div.payment_box :-ms-input-placeholder,
.woocommerce-checkout #payment div.payment_box :-ms-input-placeholder,
#add_payment_method #payment div.payment_box :-ms-input-placeholder {
  color: var(--cmsmasters-colors-text);
}
.woocommerce-cart #payment div.payment_box .woocommerce-SavedPaymentMethods,
.woocommerce-checkout #payment div.payment_box .woocommerce-SavedPaymentMethods,
#add_payment_method #payment div.payment_box .woocommerce-SavedPaymentMethods {
  list-style: none outside;
  margin: 0;
}
.woocommerce-cart #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-token,
.woocommerce-cart #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-new,
.woocommerce-checkout #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-token,
.woocommerce-checkout #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-new,
#add_payment_method #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-token,
#add_payment_method #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-new {
  margin: 0 0 0.5em;
}
.woocommerce-cart #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-token label,
.woocommerce-cart #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-new label,
.woocommerce-checkout #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-token label,
.woocommerce-checkout #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-new label,
#add_payment_method #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-token label,
#add_payment_method #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-new label {
  cursor: pointer;
}
.woocommerce-cart #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-tokenInput,
.woocommerce-checkout #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-tokenInput,
#add_payment_method #payment div.payment_box .woocommerce-SavedPaymentMethods .woocommerce-SavedPaymentMethods-tokenInput {
  vertical-align: middle;
  margin: -3px 1em 0 0;
  position: relative;
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form,
#add_payment_method #payment div.payment_box .wc-credit-card-form {
  border: 0;
  padding: 0;
  margin: 1em 0 0;
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc {
  font-size: 1.5em;
  padding: 8px;
  background-repeat: no-repeat;
  background-position: right 0.618em center;
  background-size: 32px 20px;
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number.visa,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry.visa,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc.visa,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number.visa,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry.visa,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc.visa,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number.visa,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry.visa,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc.visa {
  background-image: url("../images/icons/credit-cards/visa.svg");
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number.mastercard,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry.mastercard,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc.mastercard,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number.mastercard,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry.mastercard,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc.mastercard,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number.mastercard,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry.mastercard,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc.mastercard {
  background-image: url("../images/icons/credit-cards/mastercard.svg");
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number.laser,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry.laser,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc.laser,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number.laser,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry.laser,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc.laser,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number.laser,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry.laser,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc.laser {
  background-image: url("../images/icons/credit-cards/laser.svg");
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number.dinersclub,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry.dinersclub,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc.dinersclub,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number.dinersclub,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry.dinersclub,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc.dinersclub,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number.dinersclub,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry.dinersclub,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc.dinersclub {
  background-image: url("../images/icons/credit-cards/diners.svg");
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number.maestro,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry.maestro,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc.maestro,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number.maestro,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry.maestro,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc.maestro,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number.maestro,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry.maestro,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc.maestro {
  background-image: url("../images/icons/credit-cards/maestro.svg");
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number.jcb,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry.jcb,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc.jcb,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number.jcb,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry.jcb,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc.jcb,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number.jcb,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry.jcb,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc.jcb {
  background-image: url("../images/icons/credit-cards/jcb.svg");
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number.amex,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry.amex,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc.amex,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number.amex,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry.amex,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc.amex,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number.amex,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry.amex,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc.amex {
  background-image: url("../images/icons/credit-cards/amex.svg");
}
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-number.discover,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-expiry.discover,
.woocommerce-cart #payment div.payment_box .wc-credit-card-form-card-cvc.discover,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-number.discover,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-expiry.discover,
.woocommerce-checkout #payment div.payment_box .wc-credit-card-form-card-cvc.discover,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-number.discover,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-expiry.discover,
#add_payment_method #payment div.payment_box .wc-credit-card-form-card-cvc.discover {
  background-image: url("../images/icons/credit-cards/discover.svg");
}
.woocommerce-cart #payment div.payment_box span.help,
.woocommerce-checkout #payment div.payment_box span.help,
#add_payment_method #payment div.payment_box span.help {
  font-size: 0.857em;
  color: var(--cmsmasters-colors-text);
  font-weight: normal;
}
.woocommerce-cart #payment div.payment_box .form-row,
.woocommerce-checkout #payment div.payment_box .form-row,
#add_payment_method #payment div.payment_box .form-row {
  margin: 0 0 1em;
}
.woocommerce-cart #payment div.payment_box p:last-child,
.woocommerce-checkout #payment div.payment_box p:last-child,
#add_payment_method #payment div.payment_box p:last-child {
  margin-bottom: 0;
}
.woocommerce-cart #payment .payment_method_paypal .about_paypal,
.woocommerce-checkout #payment .payment_method_paypal .about_paypal,
#add_payment_method #payment .payment_method_paypal .about_paypal {
  margin-right: 15px;
  vertical-align: middle;
}
.woocommerce-cart #payment .payment_method_paypal img,
.woocommerce-checkout #payment .payment_method_paypal img,
#add_payment_method #payment .payment_method_paypal img {
  max-height: 52px;
  vertical-align: middle;
}
.woocommerce-cart #payment .place-order,
.woocommerce-checkout #payment .place-order,
#add_payment_method #payment .place-order {
  padding: 1rem;
}
.woocommerce-cart #payment #place_order,
.woocommerce-checkout #payment #place_order,
#add_payment_method #payment #place_order {
  float: none;
}

.woocommerce-terms-and-conditions {
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.05);
}

.woocommerce-invalid #terms {
  outline: 2px solid red;
  outline-offset: 2px;
}

/**
 * Password strength meter
 */
.woocommerce-password-strength {
  text-align: center;
  font-weight: 600;
  padding: 3px 0.5em;
  font-size: 1em;
}
.woocommerce-password-strength.strong {
  background-color: #c1e1b9;
  border-color: #83c373;
}
.woocommerce-password-strength.short {
  background-color: #f1adad;
  border-color: #e35b5b;
}
.woocommerce-password-strength.bad {
  background-color: #fbc5a9;
  border-color: #f78b53;
}
.woocommerce-password-strength.good {
  background-color: #ffe399;
  border-color: #ffc733;
}

.woocommerce-password-hint {
  margin: 0.5em 0 0;
  display: block;
}

/**
 * Twenty Eleven specific styles
 */
#content.twentyeleven .woocommerce-pagination a {
  font-size: 1em;
  line-height: 1;
}

/**
 * Twenty Thirteen specific styles
 */
.single-product .twentythirteen .entry-summary,
.single-product .twentythirteen #reply-title,
.single-product .twentythirteen #respond #commentform {
  padding: 0;
}
.single-product .twentythirteen p.stars {
  clear: both;
}

.twentythirteen .woocommerce-breadcrumb {
  padding-top: 40px;
}

/**
 * Twenty Fourteen specific styles
 */
.twentyfourteen ul.products li.product {
  margin-top: 0 !important;
}

/**
 * Twenty Sixteen specific styles
 */
body:not(.search-results) .twentysixteen .entry-summary {
  color: inherit;
  font-size: inherit;
  line-height: inherit;
}

.twentysixteen .price ins {
  background: inherit;
  color: inherit;
}

/**
 * CMSMasters custom styles
 */
.add_to_cart_inline .add_to_cart_button.added {
  display: none !important;
}
.add_to_cart_inline .add_to_cart_button.added + .added_to_cart {
  opacity: 1;
}
.add_to_cart_inline a.added_to_cart {
  margin-top: 0;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}