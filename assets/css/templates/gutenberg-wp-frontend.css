@charset "UTF-8";
/* <PERSON><PERSON>nberg Widgets */
pre.wp-block-verse {
  white-space: nowrap;
  overflow: auto;
}

.has-small-font-size {
  font-size: 13px;
  line-height: 1.5em;
}

.has-regular-font-size,
.has-normal-font-size {
  font-size: 16px;
  line-height: 1.5em;
}

.has-medium-font-size {
  font-size: 20px;
  line-height: 1.5em;
}

.has-large-font-size {
  font-size: 36px;
  line-height: 1.5em;
}

.has-larger-font-size,
.has-huge-font-size {
  font-size: 42px;
  line-height: 1.5em;
}

.has-text-align-center {
  text-align: center;
}

.has-text-align-left {
  text-align: left;
}

.has-text-align-right {
  text-align: right;
}

.wp-block-archives {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

ul.wp-block-archives-list {
  list-style: none;
  margin-left: 0;
  padding-left: 0;
  color: var(--cmsmasters-colors-hover);
}
ul.wp-block-archives-list li {
  margin-top: 10px;
}
ul.wp-block-archives-list > li:first-child {
  margin-top: 0;
}
ul.wp-block-archives-list a {
  color: var(--cmsmasters-colors-heading);
  margin-right: 6px;
}
ul.wp-block-archives-list a:hover {
  color: var(--cmsmasters-colors-link);
}
ul.wp-block-archives-list ul {
  list-style: none;
}

.wp-block-audio {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-audio audio {
  display: block;
  width: 100%;
  min-width: 300px;
  outline: none;
}

blockquote.wp-block-quote, blockquote.wp-block-quote.is-large, blockquote.wp-block-quote.is-style-large {
  font-family: var(--cmsmasters-blockquote-font-family);
  font-weight: var(--cmsmasters-blockquote-font-weight);
  font-style: var(--cmsmasters-blockquote-font-style);
  text-transform: var(--cmsmasters-blockquote-text-transform);
  text-decoration: var(--cmsmasters-blockquote-text-decoration);
  font-size: var(--cmsmasters-blockquote-font-size);
  line-height: var(--cmsmasters-blockquote-line-height);
  letter-spacing: var(--cmsmasters-blockquote-letter-spacing);
  word-spacing: var(--cmsmasters-blockquote-word-spacing);
  background-color: var(--cmsmasters-blockquote-colors-bg);
  border-style: var(--cmsmasters-blockquote-bd-style);
  border-top-width: var(--cmsmasters-blockquote-bd-width-top);
  border-right-width: var(--cmsmasters-blockquote-bd-width-right);
  border-bottom-width: var(--cmsmasters-blockquote-bd-width-bottom);
  border-left-width: var(--cmsmasters-blockquote-bd-width-left);
  border-color: var(--cmsmasters-blockquote-colors-bd);
  border-radius: var(--cmsmasters-blockquote-bd-radius);
  box-shadow: var(--cmsmasters-blockquote-box-shadow);
  padding-top: var(--cmsmasters-blockquote-padding-top);
  padding-right: var(--cmsmasters-blockquote-padding-right);
  padding-bottom: var(--cmsmasters-blockquote-padding-bottom);
  padding-left: var(--cmsmasters-blockquote-padding-left);
  margin: 3rem 0 3rem;
  position: relative;
  quotes: none;
}
blockquote.wp-block-quote > *, blockquote.wp-block-quote.is-large > *, blockquote.wp-block-quote.is-style-large > * {
  position: relative;
}
blockquote.wp-block-quote:after, blockquote.wp-block-quote.is-large:after, blockquote.wp-block-quote.is-style-large:after {
  content: none;
}
blockquote.wp-block-quote:before, blockquote.wp-block-quote.is-large:before, blockquote.wp-block-quote.is-style-large:before {
  content: "”" !important;
  font-family: var(--cmsmasters-blockquote-icon-font-family);
  font-weight: var(--cmsmasters-blockquote-icon-font-weight);
  font-size: var(--cmsmasters-blockquote-icon-font-size);
  line-height: 1px;
  color: var(--cmsmasters-blockquote-icon-color);
  display: var(--cmsmasters-blockquote-icon-visibility);
  justify-content: var(--cmsmasters-blockquote-icon-horizontal-alignment);
  align-items: var(--cmsmasters-blockquote-icon-vertical-alignment);
  width: 100%;
  height: 100%;
  position: absolute;
  left: var(--cmsmasters-blockquote-icon-horizontal-offset);
  top: var(--cmsmasters-blockquote-icon-vertical-offset);
}
blockquote.wp-block-quote:not(.has-text-color), blockquote.wp-block-quote.is-large:not(.has-text-color), blockquote.wp-block-quote.is-style-large:not(.has-text-color) {
  color: var(--cmsmasters-blockquote-colors-text);
}
blockquote.wp-block-quote:not(.has-text-color) a, blockquote.wp-block-quote.is-large:not(.has-text-color) a, blockquote.wp-block-quote.is-style-large:not(.has-text-color) a {
  color: var(--cmsmasters-blockquote-colors-link);
}
blockquote.wp-block-quote:not(.has-text-color) a:hover, blockquote.wp-block-quote.is-large:not(.has-text-color) a:hover, blockquote.wp-block-quote.is-style-large:not(.has-text-color) a:hover {
  color: var(--cmsmasters-blockquote-colors-hover);
}
blockquote.wp-block-quote.has-text-color a, blockquote.wp-block-quote.is-large.has-text-color a, blockquote.wp-block-quote.is-style-large.has-text-color a {
  color: inherit;
  opacity: 0.8;
  transition: opacity 0.3s ease-in-out;
}
blockquote.wp-block-quote.has-text-color a:hover, blockquote.wp-block-quote.is-large.has-text-color a:hover, blockquote.wp-block-quote.is-style-large.has-text-color a:hover {
  color: inherit;
  opacity: 1;
}
blockquote.wp-block-quote p:last-of-type, blockquote.wp-block-quote.is-large p:last-of-type, blockquote.wp-block-quote.is-style-large p:last-of-type {
  margin: 0;
  padding: 0;
}
blockquote.wp-block-quote cite, blockquote.wp-block-quote.is-large cite, blockquote.wp-block-quote.is-style-large cite {
  font-family: var(--cmsmasters-blockquote-cite-font-family);
  font-weight: var(--cmsmasters-blockquote-cite-font-weight);
  font-style: var(--cmsmasters-blockquote-cite-font-style);
  text-transform: var(--cmsmasters-blockquote-cite-text-transform);
  text-decoration: var(--cmsmasters-blockquote-cite-text-decoration);
  font-size: var(--cmsmasters-blockquote-cite-font-size);
  line-height: var(--cmsmasters-blockquote-cite-line-height);
  letter-spacing: var(--cmsmasters-blockquote-cite-letter-spacing);
  word-spacing: var(--cmsmasters-blockquote-cite-word-spacing);
  color: var(--cmsmasters-blockquote-cite-color);
  display: block;
  margin-top: var(--cmsmasters-blockquote-cite-gap);
}
blockquote.wp-block-quote.has-text-color cite, blockquote.wp-block-quote.is-large.has-text-color cite, blockquote.wp-block-quote.is-style-large.has-text-color cite {
  color: inherit;
}
blockquote.wp-block-quote .editor-rich-text__tinymce, blockquote.wp-block-quote.is-large .editor-rich-text__tinymce, blockquote.wp-block-quote.is-style-large .editor-rich-text__tinymce {
  line-height: inherit;
}
blockquote.wp-block-quote p,
blockquote.wp-block-quote cite,
blockquote.wp-block-quote footer,
blockquote.wp-block-quote .wp-block-quote__citation, blockquote.wp-block-quote.is-large p,
blockquote.wp-block-quote.is-large cite,
blockquote.wp-block-quote.is-large footer,
blockquote.wp-block-quote.is-large .wp-block-quote__citation, blockquote.wp-block-quote.is-style-large p,
blockquote.wp-block-quote.is-style-large cite,
blockquote.wp-block-quote.is-style-large footer,
blockquote.wp-block-quote.is-style-large .wp-block-quote__citation {
  text-align: inherit;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
  font-style: inherit;
}
blockquote.wp-block-quote cite,
blockquote.wp-block-quote footer,
blockquote.wp-block-quote .wp-block-quote__citation, blockquote.wp-block-quote.is-large cite,
blockquote.wp-block-quote.is-large footer,
blockquote.wp-block-quote.is-large .wp-block-quote__citation, blockquote.wp-block-quote.is-style-large cite,
blockquote.wp-block-quote.is-style-large footer,
blockquote.wp-block-quote.is-style-large .wp-block-quote__citation {
  font-family: var(--cmsmasters-blockquote-cite-font-family);
  font-weight: var(--cmsmasters-blockquote-cite-font-weight);
  font-style: var(--cmsmasters-blockquote-cite-font-style);
  text-transform: var(--cmsmasters-blockquote-cite-text-transform);
  text-decoration: var(--cmsmasters-blockquote-cite-text-decoration);
  font-size: var(--cmsmasters-blockquote-cite-font-size);
  line-height: var(--cmsmasters-blockquote-cite-line-height);
  letter-spacing: var(--cmsmasters-blockquote-cite-letter-spacing);
  word-spacing: var(--cmsmasters-blockquote-cite-word-spacing);
  color: var(--cmsmasters-blockquote-cite-color);
  display: block;
  margin-top: var(--cmsmasters-blockquote-cite-gap);
}

.wp-block-button {
  display: block;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-button__link {
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  color: var(--cmsmasters-button-normal-colors-color);
  border-color: var(--cmsmasters-button-normal-colors-bd);
  border-style: var(--cmsmasters-button-normal-bd-style);
  border-top-width: var(--cmsmasters-button-normal-bd-width-top);
  border-right-width: var(--cmsmasters-button-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-button-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-button-normal-bd-width-left);
  border-radius: var(--cmsmasters-button-normal-bd-radius);
  text-shadow: var(--cmsmasters-button-text-shadow);
  box-shadow: var(--cmsmasters-button-normal-box-shadow);
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  display: inline-block;
  cursor: pointer;
  word-break: normal;
  position: relative;
  z-index: 0;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  background-color: transparent;
  background-image: none;
}
.wp-block-button__link:hover {
  color: var(--cmsmasters-button-hover-colors-color);
  border-color: var(--cmsmasters-button-hover-colors-bd);
  border-radius: var(--cmsmasters-button-hover-bd-radius);
  text-decoration: var(--cmsmasters-button-hover-text-decoration);
  text-shadow: var(--cmsmasters-button-hover-text-shadow);
  box-shadow: var(--cmsmasters-button-hover-box-shadow);
}
.wp-block-button__link:before, .wp-block-button__link:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
}
.wp-block-button__link:before {
  background-color: var(--cmsmasters-button-normal-colors-bg);
  background-image: var(--cmsmasters-button-normal-colors-bg-image);
  opacity: 1;
}
.wp-block-button__link:after {
  background-color: var(--cmsmasters-button-hover-colors-bg);
  background-image: var(--cmsmasters-button-hover-colors-bg-image);
  opacity: 0;
}
.wp-block-button__link:hover:before {
  opacity: 0;
}
.wp-block-button__link:hover:after {
  opacity: 1;
}
.wp-block-button__link.no-border-radius {
  border-radius: 0;
}
.wp-block-button__link.has-background {
  border-color: transparent;
}
.wp-block-button__link.has-background:before, .wp-block-button__link.has-background:after {
  content: none;
}
.wp-block-button.no-border-radius, .wp-block-button__link.no-border-radius {
  border-radius: 0 !important;
}
.wp-block-button.is-style-squared > .wp-block-button__link, .wp-block-button__link.is-style-squared {
  border-radius: 0 !important;
}
.wp-block-button.is-style-outline > .wp-block-button__link, .wp-block-button__link.is-style-outline {
  color: var(--cmsmasters-colors-link);
  border: 2px solid currentColor;
}
.wp-block-button.is-style-outline > .wp-block-button__link:not(.has-background), .wp-block-button__link.is-style-outline:not(.has-background) {
  background-color: transparent;
}
.wp-block-button.is-style-outline > .wp-block-button__link:before, .wp-block-button.is-style-outline > .wp-block-button__link:after, .wp-block-button__link.is-style-outline:before, .wp-block-button__link.is-style-outline:after {
  content: none;
}

.wp-block-buttons {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-buttons.aligncenter {
  justify-content: center;
}
.wp-block-buttons .wp-block-button {
  display: inline-block;
  margin: 0;
}
.wp-block-buttons .wp-block-button + .wp-block-button {
  margin-left: 15px;
}

.wp-block-calendar {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-calendar table {
  text-align: center;
  margin-bottom: 0;
}
.wp-block-calendar .wp-calendar-nav {
  margin-top: 10px;
}

.wp-calendar-table #today {
  color: var(--cmsmasters-colors-link);
}

.wp-block-categories {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-categories ul {
  padding: 0;
}
.wp-block-categories ul ul {
  margin-top: 0;
}

ul.wp-block-categories,
.wp-block-categories > ul {
  list-style: none;
  margin-left: 0;
  padding-left: 0;
  color: var(--cmsmasters-colors-hover);
}
ul.wp-block-categories li,
.wp-block-categories > ul li {
  margin-top: 10px;
}
ul.wp-block-categories > li:first-child,
.wp-block-categories > ul > li:first-child {
  margin-top: 0;
}
ul.wp-block-categories a,
.wp-block-categories > ul a {
  color: var(--cmsmasters-colors-heading);
  margin-right: 6px;
}
ul.wp-block-categories a:hover,
.wp-block-categories > ul a:hover {
  color: var(--cmsmasters-colors-link);
}
ul.wp-block-categories ul,
.wp-block-categories > ul ul {
  list-style: none;
}

.wp-block-columns {
  display: flex;
  flex-wrap: wrap !important;
  margin: 0;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-columns {
    flex-wrap: nowrap !important;
  }
}
.wp-block-columns.are-vertically-aligned-top {
  align-items: flex-start;
}
.wp-block-columns.are-vertically-aligned-center {
  align-items: center;
}
.wp-block-columns.are-vertically-aligned-bottom {
  align-items: flex-end;
}
@media only screen and (max-width: ELEMENTOR_SCREEN_MOBILE_MAX) {
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    flex-basis: 100% !important;
  }
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    flex-basis: 0;
    flex-grow: 1;
  }
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column[style*=flex-basis] {
    flex-grow: 0;
  }
}
.wp-block-columns.is-not-stacked-on-mobile {
  flex-wrap: nowrap !important;
}
.wp-block-columns.is-not-stacked-on-mobile > .wp-block-column {
  flex-basis: 0;
  flex-grow: 1;
}
.wp-block-columns.is-not-stacked-on-mobile > .wp-block-column[style*=flex-basis] {
  flex-grow: 0;
}

.wp-block-column {
  flex-basis: 100%;
  flex-grow: 1;
  min-width: 0;
  word-break: break-word;
  overflow-wrap: break-word;
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
}
.wp-block-column:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
@media only screen and (max-width: ELEMENTOR_SCREEN_MOBILE_MAX) {
  .wp-block-column {
    flex-basis: 100% !important;
  }
}
.wp-block-column:not(:first-child) {
  margin-top: var(--cmsmasters-gutenberg-columns-gap);
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-column:not(:first-child) {
    margin-top: 0;
    margin-left: var(--cmsmasters-gutenberg-columns-gap);
  }
}
.wp-block-column.is-vertically-aligned-top {
  align-self: flex-start;
}
.wp-block-column.is-vertically-aligned-center {
  -ms-grid-row-align: center;
  align-self: center;
}
.wp-block-column.is-vertically-aligned-bottom {
  align-self: flex-end;
}
.wp-block-column.is-vertically-aligned-top, .wp-block-column.is-vertically-aligned-center, .wp-block-column.is-vertically-aligned-bottom {
  width: 100%;
}
.wp-block-column > *:first-child {
  margin-top: 0;
}
.wp-block-column > *:last-child {
  margin-bottom: 0;
}

.wp-block-cover,
.wp-block-cover-image {
  position: relative;
  background-color: #000;
  background-size: cover;
  background-position: center center;
  min-height: 430px;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  width: 100%;
  padding: 10px 20px;
  color: var(--cmsmasters-colors-bg);
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-cover.has-parallax,
.wp-block-cover-image.has-parallax {
  background-attachment: fixed;
}
@supports (-webkit-overflow-scrolling: touch) {
  .wp-block-cover.has-parallax,
  .wp-block-cover-image.has-parallax {
    background-attachment: scroll;
  }
}
@media (prefers-reduced-motion: reduce) {
  .wp-block-cover.has-parallax,
  .wp-block-cover-image.has-parallax {
    background-attachment: scroll;
  }
}
.wp-block-cover.has-background-dim:before,
.wp-block-cover-image.has-background-dim:before {
  content: "";
  background-color: inherit;
}
.wp-block-cover.has-background-dim:not(.has-background-gradient):before,
.wp-block-cover .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim:not(.has-background-gradient):before,
.wp-block-cover-image .wp-block-cover__gradient-background {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1;
  opacity: 0.5;
}
.wp-block-cover.has-background-dim.has-background-dim-10:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-10 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-10:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-10 .wp-block-cover__gradient-background {
  opacity: 0.1;
}
.wp-block-cover.has-background-dim.has-background-dim-20:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-20 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-20:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-20 .wp-block-cover__gradient-background {
  opacity: 0.2;
}
.wp-block-cover.has-background-dim.has-background-dim-30:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-30 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-30:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-30 .wp-block-cover__gradient-background {
  opacity: 0.3;
}
.wp-block-cover.has-background-dim.has-background-dim-40:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-40 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-40:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-40 .wp-block-cover__gradient-background {
  opacity: 0.4;
}
.wp-block-cover.has-background-dim.has-background-dim-50:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-50 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-50:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-50 .wp-block-cover__gradient-background {
  opacity: 0.5;
}
.wp-block-cover.has-background-dim.has-background-dim-60:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-60 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-60:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-60 .wp-block-cover__gradient-background {
  opacity: 0.6;
}
.wp-block-cover.has-background-dim.has-background-dim-70:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-70 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-70:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-70 .wp-block-cover__gradient-background {
  opacity: 0.7;
}
.wp-block-cover.has-background-dim.has-background-dim-80:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-80 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-80:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-80 .wp-block-cover__gradient-background {
  opacity: 0.8;
}
.wp-block-cover.has-background-dim.has-background-dim-90:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-90 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-90:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-90 .wp-block-cover__gradient-background {
  opacity: 0.9;
}
.wp-block-cover.has-background-dim.has-background-dim-100:not(.has-background-gradient):before,
.wp-block-cover.has-background-dim.has-background-dim-100 .wp-block-cover__gradient-background,
.wp-block-cover-image.has-background-dim.has-background-dim-100:not(.has-background-gradient):before,
.wp-block-cover-image.has-background-dim.has-background-dim-100 .wp-block-cover__gradient-background {
  opacity: 1;
}
.wp-block-cover.alignleft, .wp-block-cover.alignright,
.wp-block-cover-image.alignleft,
.wp-block-cover-image.alignright {
  max-width: 420px;
  width: 100%;
}
.wp-block-cover:after,
.wp-block-cover-image:after {
  content: "";
  display: block;
  font-size: 0;
  min-height: inherit;
}
@supports (position: sticky) {
  .wp-block-cover:after,
  .wp-block-cover-image:after {
    content: none;
  }
}
.wp-block-cover.aligncenter, .wp-block-cover.alignleft, .wp-block-cover.alignright,
.wp-block-cover-image.aligncenter,
.wp-block-cover-image.alignleft,
.wp-block-cover-image.alignright {
  display: flex;
}
.wp-block-cover.alignleft,
.wp-block-cover-image.alignleft {
  margin-left: calc(max(0px, ((100vw - var(--cmsmasters-main-content-width)) / 5)) * -1);
}
.wp-block-column .wp-block-cover.alignleft, .cmsmasters-content-layout-l-sidebar .wp-block-cover.alignleft,
.wp-block-column .wp-block-cover-image.alignleft,
.cmsmasters-content-layout-l-sidebar .wp-block-cover-image.alignleft {
  margin-left: 0;
}
.wp-block-cover.alignright,
.wp-block-cover-image.alignright {
  margin-right: calc(max(0px, ((100vw - var(--cmsmasters-main-content-width)) / 5)) * -1);
}
.wp-block-column .wp-block-cover.alignright, .cmsmasters-content-layout-r-sidebar .wp-block-cover.alignright,
.wp-block-column .wp-block-cover-image.alignright,
.cmsmasters-content-layout-r-sidebar .wp-block-cover-image.alignright {
  margin-right: 0;
}
.wp-block-cover .wp-block-cover-text,
.wp-block-cover-image .wp-block-cover-text {
  z-index: 1;
}
.wp-block-cover .wp-block-cover__inner-container,
.wp-block-cover-image .wp-block-cover__inner-container {
  width: 100%;
  z-index: 1;
}
.wp-block-cover .wp-block-cover__inner-container > *:first-child,
.wp-block-cover-image .wp-block-cover__inner-container > *:first-child {
  margin-top: 0;
}
.wp-block-cover .wp-block-cover__inner-container > *:last-child,
.wp-block-cover-image .wp-block-cover__inner-container > *:last-child {
  margin-bottom: 0;
}
.wp-block-cover p:not(.has-text-color),
.wp-block-cover h1:not(.has-text-color),
.wp-block-cover h2:not(.has-text-color),
.wp-block-cover h3:not(.has-text-color),
.wp-block-cover h4:not(.has-text-color),
.wp-block-cover h5:not(.has-text-color),
.wp-block-cover h6:not(.has-text-color),
.wp-block-cover .wp-block-subhead:not(.has-text-color),
.wp-block-cover-image p:not(.has-text-color),
.wp-block-cover-image h1:not(.has-text-color),
.wp-block-cover-image h2:not(.has-text-color),
.wp-block-cover-image h3:not(.has-text-color),
.wp-block-cover-image h4:not(.has-text-color),
.wp-block-cover-image h5:not(.has-text-color),
.wp-block-cover-image h6:not(.has-text-color),
.wp-block-cover-image .wp-block-subhead:not(.has-text-color) {
  color: inherit;
}
.wp-block-cover p a,
.wp-block-cover h1 a,
.wp-block-cover h2 a,
.wp-block-cover h3 a,
.wp-block-cover h4 a,
.wp-block-cover h5 a,
.wp-block-cover h6 a,
.wp-block-cover .wp-block-subhead a,
.wp-block-cover-image p a,
.wp-block-cover-image h1 a,
.wp-block-cover-image h2 a,
.wp-block-cover-image h3 a,
.wp-block-cover-image h4 a,
.wp-block-cover-image h5 a,
.wp-block-cover-image h6 a,
.wp-block-cover-image .wp-block-subhead a {
  color: inherit;
}
.wp-block-cover.is-position-top-left,
.wp-block-cover-image.is-position-top-left {
  align-items: flex-start;
  justify-content: flex-start;
}
.wp-block-cover.is-position-top-center,
.wp-block-cover-image.is-position-top-center {
  align-items: flex-start;
  justify-content: center;
}
.wp-block-cover.is-position-top-right,
.wp-block-cover-image.is-position-top-right {
  align-items: flex-start;
  justify-content: flex-end;
}
.wp-block-cover.is-position-center-left,
.wp-block-cover-image.is-position-center-left {
  align-items: center;
  justify-content: flex-start;
}
.wp-block-cover.is-position-center-center,
.wp-block-cover-image.is-position-center-center {
  align-items: center;
  justify-content: center;
}
.wp-block-cover.is-position-center-right,
.wp-block-cover-image.is-position-center-right {
  align-items: center;
  justify-content: flex-end;
}
.wp-block-cover.is-position-bottom-left,
.wp-block-cover-image.is-position-bottom-left {
  align-items: flex-end;
  justify-content: flex-start;
}
.wp-block-cover.is-position-bottom-center,
.wp-block-cover-image.is-position-bottom-center {
  align-items: flex-end;
  justify-content: center;
}
.wp-block-cover.is-position-bottom-right,
.wp-block-cover-image.is-position-bottom-right {
  align-items: flex-end;
  justify-content: flex-end;
}
.wp-block-cover.has-custom-content-position.has-custom-content-position .wp-block-cover__inner-container,
.wp-block-cover-image.has-custom-content-position.has-custom-content-position .wp-block-cover__inner-container {
  margin: 0;
  width: auto;
}
.wp-block-cover img.wp-block-cover__image-background,
.wp-block-cover video.wp-block-cover__video-background,
.wp-block-cover-image img.wp-block-cover__image-background,
.wp-block-cover-image video.wp-block-cover__video-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  max-width: none;
  max-height: none;
  object-fit: cover;
  outline: none;
  border: none;
  box-shadow: none;
}
.wp-block-cover .wp-block-cover__video-background,
.wp-block-cover-image .wp-block-cover__video-background {
  z-index: z-index(".wp-block-cover__video-background");
}
.wp-block-cover .wp-block-cover__image-background,
.wp-block-cover-image .wp-block-cover__image-background {
  z-index: z-index(".wp-block-cover__image-background");
}
.wp-block-cover.has-left-content,
.wp-block-cover-image.has-left-content {
  justify-content: flex-start;
}
.wp-block-cover.has-right-content,
.wp-block-cover-image.has-right-content {
  justify-content: flex-end;
}

.block-editor-block-list__block[data-type^=core-embed][data-align=left],
.block-editor-block-list__block[data-type^=core-embed][data-align=right],
.wp-block[data-align=left] > [data-type^=core-embed],
.wp-block[data-align=right] > [data-type^=core-embed],
.wp-block-embed.alignleft,
.wp-block-embed.alignright {
  max-width: 360px;
  width: 100%;
}
.block-editor-block-list__block[data-type^=core-embed][data-align=left] .wp-block-embed__wrapper,
.block-editor-block-list__block[data-type^=core-embed][data-align=right] .wp-block-embed__wrapper,
.wp-block[data-align=left] > [data-type^=core-embed] .wp-block-embed__wrapper,
.wp-block[data-align=right] > [data-type^=core-embed] .wp-block-embed__wrapper,
.wp-block-embed.alignleft .wp-block-embed__wrapper,
.wp-block-embed.alignright .wp-block-embed__wrapper {
  width: 360px;
}
.block-editor-block-list__block[data-type^=core-embed][data-align=left] iframe,
.block-editor-block-list__block[data-type^=core-embed][data-align=right] iframe,
.wp-block[data-align=left] > [data-type^=core-embed] iframe,
.wp-block[data-align=right] > [data-type^=core-embed] iframe,
.wp-block-embed.alignleft iframe,
.wp-block-embed.alignright iframe {
  width: 100%;
}

.wp-block-embed {
  text-align: center;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-embed iframe {
  width: auto;
  max-width: 100%;
  margin-top: 0;
  margin-bottom: 0;
}
.wp-block-embed.wp-block-embed-twitter iframe {
  width: 500px;
}
.wp-block-embed .twitter-tweet-rendered {
  margin-bottom: 0 !important;
}

.wp-block-embed__wrapper {
  display: flex;
  justify-content: center;
  position: relative;
}

.wp-embed-responsive .wp-has-aspect-ratio .wp-block-embed__wrapper:before {
  content: "";
  display: block;
  padding-top: 50%;
}
.wp-embed-responsive .wp-has-aspect-ratio iframe {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.wp-embed-responsive .wp-embed-aspect-21-9 .wp-block-embed__wrapper:before {
  padding-top: 42.85%;
}
.wp-embed-responsive .wp-embed-aspect-18-9 .wp-block-embed__wrapper:before {
  padding-top: 50%;
}
.wp-embed-responsive .wp-embed-aspect-16-9 .wp-block-embed__wrapper:before {
  padding-top: 56.25%;
}
.wp-embed-responsive .wp-embed-aspect-4-3 .wp-block-embed__wrapper:before {
  padding-top: 75%;
}
.wp-embed-responsive .wp-embed-aspect-1-1 .wp-block-embed__wrapper:before {
  padding-top: 100%;
}
.wp-embed-responsive .wp-embed-aspect-9-6 .wp-block-embed__wrapper:before {
  padding-top: 66.66%;
}
.wp-embed-responsive .wp-embed-aspect-1-2 .wp-block-embed__wrapper:before {
  padding-top: 200%;
}

blockquote.instagram-media,
iframe.instagram-media {
  margin: auto !important;
}

.wp-block-file {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-file > * {
  vertical-align: middle;
}
.wp-block-file .wp-block-file__button {
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  color: var(--cmsmasters-button-normal-colors-color);
  border-color: var(--cmsmasters-button-normal-colors-bd);
  border-style: var(--cmsmasters-button-normal-bd-style);
  border-top-width: var(--cmsmasters-button-normal-bd-width-top);
  border-right-width: var(--cmsmasters-button-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-button-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-button-normal-bd-width-left);
  border-radius: var(--cmsmasters-button-normal-bd-radius);
  text-shadow: var(--cmsmasters-button-text-shadow);
  box-shadow: var(--cmsmasters-button-normal-box-shadow);
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  display: inline-block;
  cursor: pointer;
  word-break: normal;
  position: relative;
  z-index: 0;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  background-color: transparent;
  background-image: none;
}
.wp-block-file .wp-block-file__button:hover {
  color: var(--cmsmasters-button-hover-colors-color);
  border-color: var(--cmsmasters-button-hover-colors-bd);
  border-radius: var(--cmsmasters-button-hover-bd-radius);
  text-decoration: var(--cmsmasters-button-hover-text-decoration);
  text-shadow: var(--cmsmasters-button-hover-text-shadow);
  box-shadow: var(--cmsmasters-button-hover-box-shadow);
}
.wp-block-file .wp-block-file__button:before, .wp-block-file .wp-block-file__button:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
}
.wp-block-file .wp-block-file__button:before {
  background-color: var(--cmsmasters-button-normal-colors-bg);
  background-image: var(--cmsmasters-button-normal-colors-bg-image);
  opacity: 1;
}
.wp-block-file .wp-block-file__button:after {
  background-color: var(--cmsmasters-button-hover-colors-bg);
  background-image: var(--cmsmasters-button-hover-colors-bg-image);
  opacity: 0;
}
.wp-block-file .wp-block-file__button:hover:before {
  opacity: 0;
}
.wp-block-file .wp-block-file__button:hover:after {
  opacity: 1;
}
.wp-block-file * + .wp-block-file__button {
  margin-left: 20px;
}

.wp-block-gallery:not(.has-nested-images),
.blocks-gallery-grid:not(.has-nested-images) {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin-top: calc(var(--cmsmasters-gutenberg-gallery-columns-gap) * -1);
  margin-left: calc(var(--cmsmasters-gutenberg-gallery-columns-gap) * -1);
  margin-right: 0;
  margin-bottom: 0;
  padding: 0;
}

.wp-block-gallery:not(.has-nested-images) {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.wp-block-gallery:not(.has-nested-images) .blocks-gallery-image,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  justify-content: center;
  margin-top: var(--cmsmasters-gutenberg-gallery-columns-gap);
  margin-left: var(--cmsmasters-gutenberg-gallery-columns-gap);
  margin-right: 0;
  margin-bottom: 0;
  position: relative;
}
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-image figure,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figure,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figure,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figure {
  margin: 0;
  height: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
}
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-image img,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item img,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image img,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item img {
  display: block;
  width: 100%;
  max-width: 100%;
  height: auto;
}
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-image figcaption,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figcaption,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption {
  text-align: center;
  font-family: var(--cmsmasters-caption-inside-font-family);
  font-weight: var(--cmsmasters-caption-inside-font-weight);
  font-style: var(--cmsmasters-caption-inside-font-style);
  text-transform: var(--cmsmasters-caption-inside-text-transform);
  text-decoration: var(--cmsmasters-caption-inside-text-decoration);
  font-size: var(--cmsmasters-caption-inside-font-size);
  line-height: var(--cmsmasters-caption-inside-line-height);
  letter-spacing: var(--cmsmasters-caption-inside-letter-spacing);
  word-spacing: var(--cmsmasters-caption-inside-word-spacing);
  color: var(--cmsmasters-caption-inside-colors-text);
  background-color: var(--cmsmasters-caption-inside-colors-bg);
  border-color: var(--cmsmasters-caption-inside-colors-bd);
  border-style: var(--cmsmasters-caption-inside-bd-style);
  border-top-width: var(--cmsmasters-caption-inside-bd-width-top);
  border-right-width: var(--cmsmasters-caption-inside-bd-width-right);
  border-bottom-width: var(--cmsmasters-caption-inside-bd-width-bottom);
  border-left-width: var(--cmsmasters-caption-inside-bd-width-left);
  border-radius: var(--cmsmasters-caption-inside-bd-radius);
  padding-top: var(--cmsmasters-caption-inside-padding-top);
  padding-right: var(--cmsmasters-caption-inside-padding-right);
  padding-bottom: var(--cmsmasters-caption-inside-padding-bottom);
  padding-left: var(--cmsmasters-caption-inside-padding-left);
  width: 100%;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: normal;
  max-height: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 0;
}
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-image figcaption a,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption a,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figcaption a,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption a {
  color: var(--cmsmasters-caption-inside-colors-link);
}
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-image figcaption a:hover,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption a:hover,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figcaption a:hover,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption a:hover {
  color: var(--cmsmasters-caption-inside-colors-hover);
}
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-image figcaption img,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption img,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figcaption img,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption img {
  display: inline;
}
.wp-block-gallery:not(.has-nested-images) figcaption,
.blocks-gallery-grid:not(.has-nested-images) figcaption {
  flex-grow: 1;
}
.wp-block-gallery:not(.has-nested-images).is-cropped .blocks-gallery-image a,
.wp-block-gallery:not(.has-nested-images).is-cropped .blocks-gallery-image img, .wp-block-gallery:not(.has-nested-images).is-cropped .blocks-gallery-item a,
.wp-block-gallery:not(.has-nested-images).is-cropped .blocks-gallery-item img,
.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-image a,
.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-image img,
.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-item a,
.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-item img {
  width: 100%;
  height: 100%;
  flex: 1;
  object-fit: cover;
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-gallery:not(.has-nested-images).columns-2 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-2 .blocks-gallery-item,
  .blocks-gallery-grid:not(.has-nested-images).columns-2 .blocks-gallery-image,
  .blocks-gallery-grid:not(.has-nested-images).columns-2 .blocks-gallery-item {
    width: calc((100% / 2) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-gallery:not(.has-nested-images).columns-3 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-3 .blocks-gallery-item,
  .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-image,
  .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-item {
    width: calc((100% / 3) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-gallery:not(.has-nested-images).columns-4 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-4 .blocks-gallery-item,
  .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-image,
  .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-item {
    width: calc((100% / 4) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-gallery:not(.has-nested-images).columns-5 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-5 .blocks-gallery-item,
  .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-image,
  .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-item {
    width: calc((100% / 5) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-gallery:not(.has-nested-images).columns-6 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-6 .blocks-gallery-item,
  .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-image,
  .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-item {
    width: calc((100% / 6) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-gallery:not(.has-nested-images).columns-7 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-7 .blocks-gallery-item,
  .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-image,
  .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-item {
    width: calc((100% / 7) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-gallery:not(.has-nested-images).columns-8 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-8 .blocks-gallery-item,
  .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-image,
  .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-item {
    width: calc((100% / 8) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
}
.wp-block-gallery:not(.has-nested-images).alignleft, .wp-block-gallery:not(.has-nested-images).alignright,
.blocks-gallery-grid:not(.has-nested-images).alignleft,
.blocks-gallery-grid:not(.has-nested-images).alignright {
  max-width: 420px;
  width: 100%;
}
.wp-block-gallery:not(.has-nested-images).aligncenter .blocks-gallery-item figure,
.blocks-gallery-grid:not(.has-nested-images).aligncenter .blocks-gallery-item figure {
  justify-content: center;
}

.wp-block-gallery:not(.is-cropped) .blocks-gallery-item {
  align-self: flex-start;
}

figure.wp-block-gallery.has-nested-images {
  align-items: normal;
}

.wp-block-gallery.has-nested-images {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin-top: calc(var(--cmsmasters-gutenberg-gallery-columns-gap) * -1);
  margin-left: calc(var(--cmsmasters-gutenberg-gallery-columns-gap) * -1);
  margin-right: 0;
  margin-bottom: 0;
  padding: 0;
  gap: initial !important;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-gallery.has-nested-images figure.wp-block-image:not(#individual-image) {
  width: calc((100% / 2) - var(--cmsmasters-gutenberg-gallery-columns-gap));
}
.wp-block-gallery.has-nested-images figure.wp-block-image {
  display: flex;
  justify-content: center;
  position: relative;
  flex-direction: column;
  max-width: 100%;
  margin-top: var(--cmsmasters-gutenberg-gallery-columns-gap);
  margin-left: var(--cmsmasters-gutenberg-gallery-columns-gap);
  margin-right: 0;
  margin-bottom: 0;
  box-sizing: border-box;
}
.wp-block-gallery.has-nested-images figure.wp-block-image > div,
.wp-block-gallery.has-nested-images figure.wp-block-image > a {
  margin: 0;
  flex-direction: column;
  flex-grow: 1;
  width: 100%;
}
.wp-block-gallery.has-nested-images figure.wp-block-image img {
  display: block;
  height: auto;
  max-width: 100% !important;
  width: auto;
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption {
  text-align: center;
  font-family: var(--cmsmasters-caption-inside-font-family);
  font-weight: var(--cmsmasters-caption-inside-font-weight);
  font-style: var(--cmsmasters-caption-inside-font-style);
  text-transform: var(--cmsmasters-caption-inside-text-transform);
  text-decoration: var(--cmsmasters-caption-inside-text-decoration);
  font-size: var(--cmsmasters-caption-inside-font-size);
  line-height: var(--cmsmasters-caption-inside-line-height);
  letter-spacing: var(--cmsmasters-caption-inside-letter-spacing);
  word-spacing: var(--cmsmasters-caption-inside-word-spacing);
  color: var(--cmsmasters-caption-inside-colors-text);
  background-color: var(--cmsmasters-caption-inside-colors-bg);
  border-color: var(--cmsmasters-caption-inside-colors-bd);
  border-style: var(--cmsmasters-caption-inside-bd-style);
  border-top-width: var(--cmsmasters-caption-inside-bd-width-top);
  border-right-width: var(--cmsmasters-caption-inside-bd-width-right);
  border-bottom-width: var(--cmsmasters-caption-inside-bd-width-bottom);
  border-left-width: var(--cmsmasters-caption-inside-bd-width-left);
  border-radius: var(--cmsmasters-caption-inside-bd-radius);
  padding-top: var(--cmsmasters-caption-inside-padding-top);
  padding-right: var(--cmsmasters-caption-inside-padding-right);
  padding-bottom: var(--cmsmasters-caption-inside-padding-bottom);
  padding-left: var(--cmsmasters-caption-inside-padding-left);
  width: 100%;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: normal;
  max-height: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 0;
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption a {
  color: var(--cmsmasters-caption-inside-colors-link);
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption a:hover {
  color: var(--cmsmasters-caption-inside-colors-hover);
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption img {
  display: inline;
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption a {
  color: inherit;
}
.wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border img {
  box-sizing: border-box;
}
.wp-block-gallery.has-nested-images figure.wp-block-image.is-style-rounded > div,
.wp-block-gallery.has-nested-images figure.wp-block-image.is-style-rounded > a, .wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border > div,
.wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border > a {
  flex: 1 1 auto;
}
.wp-block-gallery.has-nested-images figure.wp-block-image.is-style-rounded figcaption, .wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border figcaption {
  flex: initial;
  background: none;
  color: inherit;
  margin: 0;
  padding: 10px 10px 9px;
  position: relative;
}
.wp-block-gallery.has-nested-images figcaption {
  flex-grow: 1;
  flex-basis: 100%;
  text-align: center;
}
.wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) {
  align-self: inherit;
}
.wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) > div:not(.components-drop-zone),
.wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) > a {
  display: flex;
}
.wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) a,
.wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) img {
  width: 100%;
  flex: 1 0 0%;
  height: 100%;
  object-fit: cover;
}
.wp-block-gallery.has-nested-images.columns-1 figure.wp-block-image:not(#individual-image) {
  width: 100%;
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-gallery.has-nested-images.columns-3 figure.wp-block-image:not(#individual-image) {
    width: calc((100% / 3) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
  .wp-block-gallery.has-nested-images.columns-4 figure.wp-block-image:not(#individual-image) {
    width: calc((100% / 4) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
  .wp-block-gallery.has-nested-images.columns-5 figure.wp-block-image:not(#individual-image) {
    width: calc((100% / 5) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
  .wp-block-gallery.has-nested-images.columns-6 figure.wp-block-image:not(#individual-image) {
    width: calc((100% / 6) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
  .wp-block-gallery.has-nested-images.columns-7 figure.wp-block-image:not(#individual-image) {
    width: calc((100% / 7) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
  .wp-block-gallery.has-nested-images.columns-8 figure.wp-block-image:not(#individual-image) {
    width: calc((100% / 8) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
  .wp-block-gallery.has-nested-images.columns-default figure.wp-block-image:not(#individual-image) {
    width: calc((100% / 3) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
  .wp-block-gallery.has-nested-images.columns-default figure.wp-block-image:not(#individual-image):first-child:nth-last-child(2),
  .wp-block-gallery.has-nested-images.columns-default figure.wp-block-image:not(#individual-image):first-child:nth-last-child(2) ~ figure.wp-block-image:not(#individual-image) {
    width: calc((100% / 2) - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
  .wp-block-gallery.has-nested-images.columns-default figure.wp-block-image:not(#individual-image):first-child:nth-last-child(1) {
    width: calc(100% - var(--cmsmasters-gutenberg-gallery-columns-gap));
  }
}
.wp-block-gallery.has-nested-images.alignleft, .wp-block-gallery.has-nested-images.alignright {
  max-width: 420px;
  width: 100%;
}
.wp-block-gallery.has-nested-images.aligncenter {
  justify-content: center;
}

.wp-block-group {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-group.has-background {
  padding: 10px 20px;
}
.wp-block-group__inner-container > *:first-child {
  margin-top: 0;
}
.wp-block-group__inner-container > *:last-child {
  margin-bottom: 0;
}

h1.has-background, h2.has-background, h3.has-background, h4.has-background, h5.has-background, h6.has-background {
  padding: 10px 20px;
}

.wp-block-image {
  max-width: 100vw;
}
.wp-block-image img {
  max-width: 100%;
  display: block;
  margin: 0 auto;
}
.wp-block-image .alignleft {
  margin-left: calc(max(0px, ((100vw - var(--cmsmasters-main-content-width)) / 5)) * -1);
}
.wp-block-column .wp-block-image .alignleft, .cmsmasters-content-layout-l-sidebar .wp-block-image .alignleft {
  margin-left: 0;
}
.wp-block-image .alignright {
  margin-right: calc(max(0px, ((100vw - var(--cmsmasters-main-content-width)) / 5)) * -1);
}
.wp-block-column .wp-block-image .alignright, .cmsmasters-content-layout-r-sidebar .wp-block-image .alignright {
  margin-right: 0;
}
.wp-block-image .aligncenter {
  text-align: center;
}
.wp-block-image.alignfull img, .wp-block-image.alignwide img {
  width: 100%;
}
.wp-block-image.is-style-rounded img, .wp-block-image.is-style-circle-mask img {
  border-radius: 9999px;
}

div.wp-block-image,
figure.wp-block-image {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.wp-block-latest-comments {
  list-style: none;
  margin-left: 0;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-latest-comments__comment {
  margin-top: 20px;
}
.wp-block-latest-comments > .wp-block-latest-comments__comment:first-child {
  margin-top: 0;
}
.wp-block-latest-comments.has-avatars .wp-block-latest-comments__comment {
  display: flex;
  align-items: flex-start;
}
.wp-block-latest-comments .avatar, .wp-block-latest-comments__comment-avatar {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  margin-right: 25px;
  border-radius: 50px;
}
.wp-block-latest-comments article {
  flex-grow: 1;
}
.wp-block-latest-comments__comment-meta {
  color: var(--cmsmasters-colors-text);
}
.wp-block-latest-comments__comment-meta a {
  color: var(--cmsmasters-colors-heading);
}
.wp-block-latest-comments__comment-meta a:hover {
  color: var(--cmsmasters-colors-link);
}
.wp-block-latest-comments__comment-date {
  display: block;
  color: var(--cmsmasters-colors-hover);
  font-family: var(--cmsmasters-accent-font-family);
  font-weight: var(--cmsmasters-accent-font-weight);
  font-style: var(--cmsmasters-accent-font-style);
  text-transform: var(--cmsmasters-accent-text-transform);
  text-decoration: var(--cmsmasters-accent-text-decoration);
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
  letter-spacing: var(--cmsmasters-accent-letter-spacing);
  word-spacing: var(--cmsmasters-accent-word-spacing);
}
.wp-block-latest-comments__comment-excerpt {
  margin-top: 10px;
}
.wp-block-latest-comments__comment-excerpt p {
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

.wp-block-latest-posts {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-latest-posts li {
  margin-top: 20px;
  margin-right: 0;
  margin-left: 0;
  margin-bottom: 0;
  clear: both;
}
.wp-block-latest-posts li:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  overflow: hidden;
}
.wp-block-latest-posts li:first-child {
  margin-top: 0;
}
.wp-block-latest-posts li > *:first-child {
  margin-top: 0;
}
.wp-block-latest-posts li > *:last-child {
  margin-bottom: 0;
}
.wp-block-latest-posts.is-grid {
  display: flex;
  flex-wrap: wrap;
  margin-top: -20px;
  margin-left: -20px;
  margin-right: 0;
}
.wp-block-latest-posts.is-grid li {
  width: 100%;
  margin-top: 20px;
  margin-left: 20px;
  margin-right: 0;
  margin-bottom: 0;
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-latest-posts.columns-2 li {
    width: calc(100% / 2 - 20px);
  }
  .wp-block-latest-posts.columns-3 li {
    width: calc(100% / 3 - 20px);
  }
  .wp-block-latest-posts.columns-4 li {
    width: calc(100% / 4 - 20px);
  }
  .wp-block-latest-posts.columns-5 li {
    width: calc(100% / 5 - 20px);
  }
  .wp-block-latest-posts.columns-6 li {
    width: calc(100% / 6 - 20px);
  }
}
.wp-block-latest-posts__featured-image, .wp-block-latest-posts__featured-image.alignleft, .wp-block-latest-posts__featured-image.alignright, .wp-block-latest-posts__featured-image.aligncenter {
  margin-bottom: 30px;
}
.wp-block-latest-posts__featured-image.alignleft {
  margin-right: 30px;
}
.wp-block-latest-posts__featured-image.alignright {
  margin-left: 30px;
}
.wp-block-latest-posts__featured-image img {
  height: auto;
  display: block;
  max-width: 100%;
}
.wp-block-latest-posts__featured-image.aligncenter img {
  margin: 0 auto;
}
.wp-block-latest-posts li > a {
  color: var(--cmsmasters-colors-heading);
}
.wp-block-latest-posts li > a:hover {
  color: var(--cmsmasters-colors-hover);
}
.wp-block-latest-posts__post-date {
  display: block;
  color: var(--cmsmasters-colors-hover);
  font-family: var(--cmsmasters-accent-font-family);
  font-weight: var(--cmsmasters-accent-font-weight);
  font-style: var(--cmsmasters-accent-font-style);
  text-transform: var(--cmsmasters-accent-text-transform);
  text-decoration: var(--cmsmasters-accent-text-decoration);
  font-size: var(--cmsmasters-accent-font-size);
  line-height: var(--cmsmasters-accent-line-height);
  letter-spacing: var(--cmsmasters-accent-letter-spacing);
  word-spacing: var(--cmsmasters-accent-word-spacing);
  margin-top: 6px;
}
.wp-block-latest-posts__post-excerpt {
  margin-top: 13px;
}

.wp-block-media-text {
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: 50% 1fr;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-media-text .has-media-on-the-right {
  grid-template-columns: 1fr 50%;
}
.wp-block-media-text.is-vertically-aligned-top .wp-block-media-text__content,
.wp-block-media-text.is-vertically-aligned-top .wp-block-media-text__media {
  align-self: start;
}
.wp-block-media-text .wp-block-media-text__content,
.wp-block-media-text .wp-block-media-text__media, .wp-block-media-text.is-vertically-aligned-center .wp-block-media-text__content,
.wp-block-media-text.is-vertically-aligned-center .wp-block-media-text__media {
  align-self: center;
}
.wp-block-media-text.is-vertically-aligned-bottom .wp-block-media-text__content,
.wp-block-media-text.is-vertically-aligned-bottom .wp-block-media-text__media {
  align-self: end;
}
.wp-block-media-text .wp-block-media-text__media {
  grid-column: 1;
  grid-row: 1;
  margin: 0;
}
.wp-block-media-text .wp-block-media-text__content {
  grid-column: 2;
  grid-row: 1;
  padding: 0 0 0 10%;
  word-break: break-word;
}
.wp-block-media-text.has-media-on-the-right .wp-block-media-text__media {
  grid-column: 2;
  grid-row: 1;
}
.wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
  grid-column: 1;
  grid-row: 1;
  padding-left: 0;
  padding-right: 10%;
}
.wp-block-media-text.has-background .wp-block-media-text__content {
  padding-top: 5%;
  padding-bottom: 5%;
  padding-right: 10%;
  padding-left: 10%;
}
.wp-block-media-text img,
.wp-block-media-text video {
  max-width: unset;
  width: 100%;
  vertical-align: middle;
}
.wp-block-media-text.is-image-fill figure.wp-block-media-text__media {
  height: 100%;
  min-height: 250px;
  background-size: cover;
}
.wp-block-media-text.is-image-fill figure.wp-block-media-text__media > img {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
@media only screen and (max-width: ELEMENTOR_SCREEN_TABLET_MAX) {
  .wp-block-media-text.is-stacked-on-mobile {
    grid-template-columns: 100% !important;
  }
  .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__media {
    grid-column: 1;
    grid-row: 1;
  }
  .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content {
    grid-column: 1;
    grid-row: 2;
    padding-left: 10%;
    padding-right: 10%;
  }
  .wp-block-media-text.is-stacked-on-mobile.has-background .wp-block-media-text__content {
    padding-top: 10%;
    padding-bottom: 10%;
  }
  .wp-block-media-text.is-stacked-on-mobile.has-media-on-the-right .wp-block-media-text__media {
    grid-column: 1;
    grid-row: 2;
  }
  .wp-block-media-text.is-stacked-on-mobile.has-media-on-the-right .wp-block-media-text__content {
    grid-column: 1;
    grid-row: 1;
    padding-left: 10%;
    padding-right: 10%;
  }
}
.wp-block-media-text .block-editor-inner-blocks {
  padding: 0 0 0 10%;
}
.wp-block-media-text.has-media-on-the-right .block-editor-inner-blocks {
  padding-left: 0;
  padding-right: 10%;
}
.wp-block-media-text.has-background .block-editor-inner-blocks {
  padding-left: 10%;
  padding-right: 10%;
}

.is-small-text {
  font-size: 14px;
  line-height: 1.5em;
}

.is-regular-text {
  font-size: 16px;
  line-height: 1.5em;
}

.is-large-text {
  font-size: 36px;
  line-height: 1.5em;
}

.is-larger-text {
  font-size: 48px;
  line-height: 1.5em;
}

p.has-background {
  padding: 10px 20px;
}

p.has-text-color a {
  color: inherit;
}

p.wp-block-subhead {
  font-size: 0.75em;
}

.wp-block-pullquote {
  max-width: 100vw;
  border-color: var(--cmsmasters-pullquote-colors-bd);
  border-radius: var(--cmsmasters-pullquote-bd-radius);
  padding: 0;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-pullquote > blockquote {
  font-family: var(--cmsmasters-pullquote-font-family);
  font-weight: var(--cmsmasters-pullquote-font-weight);
  font-style: var(--cmsmasters-pullquote-font-style);
  text-transform: var(--cmsmasters-pullquote-text-transform);
  text-decoration: var(--cmsmasters-pullquote-text-decoration);
  font-size: var(--cmsmasters-pullquote-font-size);
  line-height: var(--cmsmasters-pullquote-line-height);
  letter-spacing: var(--cmsmasters-pullquote-letter-spacing);
  word-spacing: var(--cmsmasters-pullquote-word-spacing);
  background-color: var(--cmsmasters-pullquote-colors-bg);
  border-style: var(--cmsmasters-pullquote-bd-style);
  border-top-width: var(--cmsmasters-pullquote-bd-width-top);
  border-right-width: var(--cmsmasters-pullquote-bd-width-right);
  border-bottom-width: var(--cmsmasters-pullquote-bd-width-bottom);
  border-left-width: var(--cmsmasters-pullquote-bd-width-left);
  border-color: var(--cmsmasters-pullquote-colors-bd);
  border-radius: var(--cmsmasters-pullquote-bd-radius);
  box-shadow: var(--cmsmasters-pullquote-box-shadow);
  padding-top: var(--cmsmasters-pullquote-padding-top);
  padding-right: var(--cmsmasters-pullquote-padding-right);
  padding-bottom: var(--cmsmasters-pullquote-padding-bottom);
  padding-left: var(--cmsmasters-pullquote-padding-left);
  margin: 3rem 0 3rem;
  position: relative;
  quotes: none;
  text-align: center;
  margin-top: 0;
  margin-bottom: 0;
  border-color: inherit;
}
.wp-block-pullquote > blockquote > * {
  position: relative;
}
.wp-block-pullquote > blockquote:after {
  content: none;
}
.wp-block-pullquote > blockquote:before {
  content: "”" !important;
  font-family: var(--cmsmasters-pullquote-icon-font-family);
  font-weight: var(--cmsmasters-pullquote-icon-font-weight);
  font-size: var(--cmsmasters-pullquote-icon-font-size);
  line-height: 1px;
  color: var(--cmsmasters-pullquote-icon-color);
  display: var(--cmsmasters-pullquote-icon-visibility);
  justify-content: var(--cmsmasters-pullquote-icon-horizontal-alignment);
  align-items: var(--cmsmasters-pullquote-icon-vertical-alignment);
  width: 100%;
  height: 100%;
  position: absolute;
  left: var(--cmsmasters-pullquote-icon-horizontal-offset);
  top: var(--cmsmasters-pullquote-icon-vertical-offset);
}
.wp-block-pullquote > blockquote:not(.has-text-color) {
  color: var(--cmsmasters-pullquote-colors-text);
}
.wp-block-pullquote > blockquote:not(.has-text-color) a {
  color: var(--cmsmasters-pullquote-colors-link);
}
.wp-block-pullquote > blockquote:not(.has-text-color) a:hover {
  color: var(--cmsmasters-pullquote-colors-hover);
}
.wp-block-pullquote > blockquote.has-text-color a {
  color: inherit;
  opacity: 0.8;
  transition: opacity 0.3s ease-in-out;
}
.wp-block-pullquote > blockquote.has-text-color a:hover {
  color: inherit;
  opacity: 1;
}
.wp-block-pullquote > blockquote p:last-of-type {
  margin: 0;
  padding: 0;
}
.wp-block-pullquote > blockquote cite {
  font-family: var(--cmsmasters-pullquote-cite-font-family);
  font-weight: var(--cmsmasters-pullquote-cite-font-weight);
  font-style: var(--cmsmasters-pullquote-cite-font-style);
  text-transform: var(--cmsmasters-pullquote-cite-text-transform);
  text-decoration: var(--cmsmasters-pullquote-cite-text-decoration);
  font-size: var(--cmsmasters-pullquote-cite-font-size);
  line-height: var(--cmsmasters-pullquote-cite-line-height);
  letter-spacing: var(--cmsmasters-pullquote-cite-letter-spacing);
  word-spacing: var(--cmsmasters-pullquote-cite-word-spacing);
  color: var(--cmsmasters-pullquote-cite-color);
  display: block;
  margin-top: var(--cmsmasters-pullquote-cite-gap);
}
.wp-block-pullquote > blockquote.has-text-color cite {
  color: inherit;
}
.wp-block-pullquote > blockquote p {
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
  font-style: inherit;
}
.wp-block-pullquote.is-style-solid-color > blockquote {
  border: 0;
  background-color: transparent;
}
.wp-block-pullquote.is-style-solid-color .wp-block-pullquote__citation, .wp-block-pullquote__citation {
  font-family: var(--cmsmasters-pullquote-cite-font-family);
  font-weight: var(--cmsmasters-pullquote-cite-font-weight);
  font-style: var(--cmsmasters-pullquote-cite-font-style);
  text-transform: var(--cmsmasters-pullquote-cite-text-transform);
  text-decoration: var(--cmsmasters-pullquote-cite-text-decoration);
  font-size: var(--cmsmasters-pullquote-cite-font-size);
  line-height: var(--cmsmasters-pullquote-cite-line-height);
  letter-spacing: var(--cmsmasters-pullquote-cite-letter-spacing);
  word-spacing: var(--cmsmasters-pullquote-cite-word-spacing);
  color: var(--cmsmasters-pullquote-cite-color);
  display: block;
  margin-top: var(--cmsmasters-pullquote-cite-gap);
}
.wp-block-pullquote .has-text-color a,
.wp-block-pullquote .has-text-color .wp-block-pullquote__citation,
.wp-block-pullquote .has-text-color cite {
  color: inherit;
}
.wp-block-pullquote .has-text-color a:hover {
  color: inherit;
}
.wp-block-pullquote.alignleft, .wp-block-pullquote.alignright {
  max-width: 100vw;
}
.wp-block-pullquote.alignleft p, .wp-block-pullquote.alignright p {
  font-size: inherit;
}
.wp-block-pullquote.alignleft {
  margin-left: calc(max(0px, ((100vw - var(--cmsmasters-main-content-width)) / 5)) * -1);
}
.wp-block-column .wp-block-pullquote.alignleft, .cmsmasters-content-layout-l-sidebar .wp-block-pullquote.alignleft {
  margin-left: 0;
}
.wp-block-pullquote.alignright {
  margin-right: calc(max(0px, ((100vw - var(--cmsmasters-main-content-width)) / 5)) * -1);
}
.wp-block-column .wp-block-pullquote.alignright, .cmsmasters-content-layout-r-sidebar .wp-block-pullquote.alignright {
  margin-right: 0;
}

.wp-block-rss {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-rss li {
  margin-top: 20px;
  margin-right: 0;
  margin-left: 0;
  margin-bottom: 0;
}
.wp-block-rss li:first-child {
  margin-top: 0;
}
.wp-block-rss.is-grid {
  display: flex;
  flex-wrap: wrap;
  margin-top: -20px;
  margin-left: -20px;
  margin-right: 0;
}
.wp-block-rss.is-grid li {
  width: 100%;
  margin-top: 20px;
  margin-left: 20px;
  margin-right: 0;
  margin-bottom: 0;
}
@media only screen and (min-width: ELEMENTOR_SCREEN_TABLET_MIN) {
  .wp-block-rss.columns-2 li {
    width: calc(100% / 2 - 20px);
  }
  .wp-block-rss.columns-3 li {
    width: calc(100% / 3 - 20px);
  }
  .wp-block-rss.columns-4 li {
    width: calc(100% / 4 - 20px);
  }
  .wp-block-rss.columns-5 li {
    width: calc(100% / 5 - 20px);
  }
  .wp-block-rss.columns-6 li {
    width: calc(100% / 6 - 20px);
  }
}
.wp-block-rss__item-publish-date, .wp-block-rss__item-author {
  display: block;
  color: var(--cmsmasters-colors-heading);
}

.wp-block-search {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-search__inside-wrapper {
  display: inline-flex;
  flex: auto;
  flex-wrap: nowrap;
  max-width: 100%;
  position: relative;
}
.wp-block-search__label {
  font-family: var(--cmsmasters-input-label-font-family);
  font-weight: var(--cmsmasters-input-label-font-weight);
  font-style: var(--cmsmasters-input-label-font-style);
  text-transform: var(--cmsmasters-input-label-text-transform);
  text-decoration: var(--cmsmasters-input-label-text-decoration);
  font-size: var(--cmsmasters-input-label-font-size);
  line-height: var(--cmsmasters-input-label-line-height);
  letter-spacing: var(--cmsmasters-input-label-letter-spacing);
  word-spacing: var(--cmsmasters-input-label-word-spacing);
  color: var(--cmsmasters-input-label-color);
  display: inline-block;
  margin-bottom: 0.75rem;
  width: 100%;
}
.wp-block-search .wp-block-search__input {
  background-image: none;
  outline: none;
  height: auto;
  width: 100%;
  max-width: 100%;
  vertical-align: middle;
  flex-grow: 1;
  text-shadow: none;
  font-family: var(--cmsmasters-input-font-family);
  font-weight: var(--cmsmasters-input-font-weight);
  font-style: var(--cmsmasters-input-font-style);
  text-transform: var(--cmsmasters-input-text-transform);
  text-decoration: var(--cmsmasters-input-text-decoration);
  font-size: var(--cmsmasters-input-font-size);
  line-height: var(--cmsmasters-input-line-height);
  letter-spacing: var(--cmsmasters-input-letter-spacing);
  word-spacing: var(--cmsmasters-input-word-spacing);
  color: var(--cmsmasters-input-normal-colors-color);
  background-color: var(--cmsmasters-input-normal-colors-bg);
  border-color: var(--cmsmasters-input-normal-colors-bd);
  border-style: var(--cmsmasters-input-normal-bd-style);
  border-top-width: var(--cmsmasters-input-normal-bd-width-top);
  border-right-width: var(--cmsmasters-input-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-input-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-input-normal-bd-width-left);
  border-radius: var(--cmsmasters-input-normal-bd-radius);
  box-shadow: var(--cmsmasters-input-normal-box-shadow);
  padding-top: var(--cmsmasters-input-padding-top);
  padding-right: var(--cmsmasters-input-padding-right);
  padding-bottom: var(--cmsmasters-input-padding-bottom);
  padding-left: var(--cmsmasters-input-padding-left);
  transition: all 0.3s ease-in-out;
  margin: 0;
  flex-grow: 1;
  min-width: 3em;
}
.wp-block-search .wp-block-search__input::-webkit-input-placeholder {
  color: var(--cmsmasters-input-placeholder-color);
  transition: color 0.2s ease-in-out;
}
.wp-block-search .wp-block-search__input::-moz-placeholder {
  color: var(--cmsmasters-input-placeholder-color);
  transition: color 0.2s ease-in-out;
}
.wp-block-search .wp-block-search__input:focus {
  color: var(--cmsmasters-input-focus-colors-color);
  background-color: var(--cmsmasters-input-focus-colors-bg);
  border-color: var(--cmsmasters-input-focus-colors-bd);
  border-radius: var(--cmsmasters-input-focus-bd-radius);
  box-shadow: var(--cmsmasters-input-focus-box-shadow);
}
.wp-block-search .wp-block-search__input:focus::-webkit-input-placeholder {
  color: transparent;
}
.wp-block-search .wp-block-search__input:focus::-moz-placeholder {
  color: transparent;
}
.wp-block-search__button {
  position: relative;
  min-width: auto;
  height: auto;
  word-break: normal;
}
.wp-block-search__button.has-icon {
  font-size: var(--cmsmasters-input-font-size) !important;
  line-height: 0 !important;
  width: 4.2em;
  height: auto;
  padding: 0 !important;
}
.wp-block-search.wp-block-search__button-outside .wp-block-search__button {
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  color: var(--cmsmasters-button-normal-colors-color);
  border-color: var(--cmsmasters-button-normal-colors-bd);
  border-style: var(--cmsmasters-button-normal-bd-style);
  border-top-width: var(--cmsmasters-button-normal-bd-width-top);
  border-right-width: var(--cmsmasters-button-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-button-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-button-normal-bd-width-left);
  border-radius: var(--cmsmasters-button-normal-bd-radius);
  text-shadow: var(--cmsmasters-button-text-shadow);
  box-shadow: var(--cmsmasters-button-normal-box-shadow);
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  display: inline-block;
  cursor: pointer;
  word-break: normal;
  position: relative;
  z-index: 0;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  background-color: transparent;
  background-image: none;
  margin-left: 10px;
  margin-right: 0;
  flex-shrink: 0;
}
.wp-block-search.wp-block-search__button-outside .wp-block-search__button:hover {
  color: var(--cmsmasters-button-hover-colors-color);
  border-color: var(--cmsmasters-button-hover-colors-bd);
  border-radius: var(--cmsmasters-button-hover-bd-radius);
  text-decoration: var(--cmsmasters-button-hover-text-decoration);
  text-shadow: var(--cmsmasters-button-hover-text-shadow);
  box-shadow: var(--cmsmasters-button-hover-box-shadow);
}
.wp-block-search.wp-block-search__button-outside .wp-block-search__button:before, .wp-block-search.wp-block-search__button-outside .wp-block-search__button:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
}
.wp-block-search.wp-block-search__button-outside .wp-block-search__button:before {
  background-color: var(--cmsmasters-button-normal-colors-bg);
  background-image: var(--cmsmasters-button-normal-colors-bg-image);
  opacity: 1;
}
.wp-block-search.wp-block-search__button-outside .wp-block-search__button:after {
  background-color: var(--cmsmasters-button-hover-colors-bg);
  background-image: var(--cmsmasters-button-hover-colors-bg-image);
  opacity: 0;
}
.wp-block-search.wp-block-search__button-outside .wp-block-search__button:hover:before {
  opacity: 0;
}
.wp-block-search.wp-block-search__button-outside .wp-block-search__button:hover:after {
  opacity: 1;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__input {
  border-right-width: 0;
  border-right-style: solid;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__input:focus + .wp-block-search__button {
  border-radius: var(--cmsmasters-input-focus-bd-radius);
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button {
  font-family: var(--cmsmasters-button-font-family);
  font-weight: var(--cmsmasters-button-font-weight);
  font-style: var(--cmsmasters-button-font-style);
  text-transform: var(--cmsmasters-button-text-transform);
  text-decoration: var(--cmsmasters-button-text-decoration);
  font-size: var(--cmsmasters-button-font-size);
  line-height: var(--cmsmasters-button-line-height);
  letter-spacing: var(--cmsmasters-button-letter-spacing);
  word-spacing: var(--cmsmasters-button-word-spacing);
  color: var(--cmsmasters-button-normal-colors-color);
  border-color: var(--cmsmasters-button-normal-colors-bd);
  border-style: var(--cmsmasters-button-normal-bd-style);
  border-top-width: var(--cmsmasters-button-normal-bd-width-top);
  border-right-width: var(--cmsmasters-button-normal-bd-width-right);
  border-bottom-width: var(--cmsmasters-button-normal-bd-width-bottom);
  border-left-width: var(--cmsmasters-button-normal-bd-width-left);
  border-radius: var(--cmsmasters-button-normal-bd-radius);
  text-shadow: var(--cmsmasters-button-text-shadow);
  box-shadow: var(--cmsmasters-button-normal-box-shadow);
  padding-top: var(--cmsmasters-button-padding-top);
  padding-right: var(--cmsmasters-button-padding-right);
  padding-bottom: var(--cmsmasters-button-padding-bottom);
  padding-left: var(--cmsmasters-button-padding-left);
  display: inline-block;
  cursor: pointer;
  word-break: normal;
  position: relative;
  z-index: 0;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  background-color: transparent;
  background-image: none;
  flex-shrink: 0;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button:hover {
  color: var(--cmsmasters-button-hover-colors-color);
  border-color: var(--cmsmasters-button-hover-colors-bd);
  border-radius: var(--cmsmasters-button-hover-bd-radius);
  text-decoration: var(--cmsmasters-button-hover-text-decoration);
  text-shadow: var(--cmsmasters-button-hover-text-shadow);
  box-shadow: var(--cmsmasters-button-hover-box-shadow);
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button:before, .wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button:before {
  background-color: var(--cmsmasters-button-normal-colors-bg);
  background-image: var(--cmsmasters-button-normal-colors-bg-image);
  opacity: 1;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button:after {
  background-color: var(--cmsmasters-button-hover-colors-bg);
  background-image: var(--cmsmasters-button-hover-colors-bg-image);
  opacity: 0;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button:hover:before {
  opacity: 0;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button:hover:after {
  opacity: 1;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__text-button .wp-block-search__button {
  border-radius: var(--cmsmasters-input-normal-bd-radius);
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button input[type=search], .wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button input[type=text] {
  width: 100%;
  margin: 0;
  padding-right: calc(1em + 5px + var(--cmsmasters-input-padding-right));
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button button {
  font-size: var(--cmsmasters-input-font-size);
  color: var(--cmsmasters-input-normal-colors-color);
  background-color: transparent;
  background-image: none;
  border: 0;
  width: 1em;
  height: 100%;
  margin: auto !important;
  position: absolute;
  left: auto;
  right: var(--cmsmasters-input-padding-right);
  top: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
  border-radius: 0;
  text-shadow: none;
  box-shadow: none;
  overflow: hidden;
  word-break: normal;
  transition: all 0.3s ease-in-out;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button button:hover {
  color: var(--cmsmasters-input-focus-colors-color);
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button button:before, .wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button button:after {
  content: none;
  display: none;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button button i,
.wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button button i:before {
  width: 1em;
  height: 1em;
  margin: auto !important;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.wp-block-search.wp-block-search__button-inside.wp-block-search__icon-button .wp-block-search__button {
  width: fit-content !important;
}

.elementor .wp-block-separator,
.wp-block-separator {
  box-sizing: content-box; /* 1 */
  height: 1px; /* 1 */
  overflow: visible; /* 2 */
  border: 0;
  color: var(--cmsmasters-colors-bd);
  background-color: var(--cmsmasters-colors-bd);
  margin-top: 3rem;
  margin-bottom: 3rem;
  clear: both;
  max-width: 300px;
}
.elementor .wp-block-separator.is-style-wide,
.wp-block-separator.is-style-wide {
  max-width: none;
  border-bottom-width: 1px;
}
.elementor .wp-block-separator.is-style-dots,
.wp-block-separator.is-style-dots {
  background: none !important;
  border: none;
  text-align: center;
  max-width: none;
  line-height: 1;
  height: auto;
}
.elementor .wp-block-separator.is-style-dots:before,
.wp-block-separator.is-style-dots:before {
  content: "···";
  color: currentColor;
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 2em;
  padding-left: 2em;
  font-family: serif;
}

.wp-block-spacer {
  clear: both;
}
.wp-block-spacer + * {
  margin-top: 0 !important;
}

.wp-block-social-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding-left: 0;
  padding-right: 0;
  text-indent: 0;
  margin-left: 0;
}
.wp-block-social-links .wp-social-link a,
.wp-block-social-links .wp-social-link a:hover {
  text-decoration: none;
  border-bottom: 0;
  box-shadow: none;
}
.wp-block-social-links.has-small-icon-size {
  font-size: 16px;
}
.wp-block-social-links, .wp-block-social-links.has-normal-icon-size {
  font-size: 24px;
}
.wp-block-social-links.has-large-icon-size {
  font-size: 36px;
}
.wp-block-social-links.has-huge-icon-size {
  font-size: 48px;
}
.wp-block-social-links .wp-social-link {
  display: block;
  font-size: inherit;
  border-radius: 9999px;
  margin-top: 0;
  margin-left: 0;
  margin-right: 10px;
  margin-bottom: 10px;
  transition: transform 0.1s ease;
}
.wp-block-social-links .wp-social-link a,
.wp-block-social-links .wp-social-link button {
  font-size: inherit;
  padding: 0.25em;
  display: block;
  line-height: 0;
  transition: transform 0.1s ease;
}
.wp-block-social-links .wp-social-link a,
.wp-block-social-links .wp-social-link a:hover,
.wp-block-social-links .wp-social-link a:active,
.wp-block-social-links .wp-social-link a:visited,
.wp-block-social-links .wp-social-link svg {
  color: currentColor;
  fill: currentColor;
}
.wp-block-social-links .wp-social-link:hover {
  transform: scale(1.1);
}
.wp-block-social-links .wp-social-link svg {
  width: 1em;
  height: 1em;
}

.wp-block-social-links.aligncenter {
  justify-content: center;
  display: flex;
}

.wp-block-social-links:not(.is-style-logos-only) .wp-social-link {
  background-color: #f0f0f0;
  color: #444;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-amazon {
  background-color: #f90;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-bandcamp {
  background-color: #1ea0c3;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-behance {
  background-color: #0757fe;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-codepen {
  background-color: #1e1f26;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-deviantart {
  background-color: #02e49b;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-dribbble {
  background-color: #e94c89;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-dropbox {
  background-color: #4280ff;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-etsy {
  background-color: #f45800;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-facebook {
  background-color: #1778f2;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-fivehundredpx {
  background-color: #000;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-flickr {
  background-color: #0461dd;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-foursquare {
  background-color: #e65678;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-github {
  background-color: #24292d;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-goodreads {
  background-color: #eceadd;
  color: #382110;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-google {
  background-color: #ea4434;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-instagram {
  background-color: #f00075;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-lastfm {
  background-color: #e21b24;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-linkedin {
  background-color: #0d66c2;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-mastodon {
  background-color: #3288d4;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-medium {
  background-color: #02ab6c;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-meetup {
  background-color: #f6405f;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-patreon {
  background-color: #ff424d;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-pinterest {
  background-color: #e60122;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-pocket {
  background-color: #ef4155;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-reddit {
  background-color: #fe4500;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-skype {
  background-color: #0478d7;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-snapchat {
  background-color: #fefc00;
  color: #fff;
  stroke: #000;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-soundcloud {
  background-color: #ff5600;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-spotify {
  background-color: #1bd760;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-telegram {
  background-color: #2aabee;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-tiktok {
  background-color: #000;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-tumblr {
  background-color: #011835;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-twitch {
  background-color: #6440a4;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-twitter {
  background-color: #1da1f2;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-vimeo {
  background-color: #1eb7ea;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-vk {
  background-color: #4680c2;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-wordpress {
  background-color: #3499cd;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-yelp {
  background-color: #d32422;
  color: #fff;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link-youtube {
  background-color: #f00;
  color: #fff;
}

.wp-block-social-links.is-style-logos-only .wp-social-link {
  background: none;
  padding: 4px;
}
.wp-block-social-links.is-style-logos-only .wp-social-link a,
.wp-block-social-links.is-style-logos-only .wp-social-link button {
  padding: 0;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-amazon {
  color: #f90;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-bandcamp {
  color: #1ea0c3;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-behance {
  color: #0757fe;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-codepen {
  color: #1e1f26;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-deviantart {
  color: #02e49b;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-dribbble {
  color: #e94c89;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-dropbox {
  color: #4280ff;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-etsy {
  color: #f45800;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-facebook {
  color: #1778f2;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-fivehundredpx {
  color: #000;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-flickr {
  color: #0461dd;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-foursquare {
  color: #e65678;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-github {
  color: #24292d;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-goodreads {
  color: #382110;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-google {
  color: #ea4434;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-instagram {
  color: #f00075;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-lastfm {
  color: #e21b24;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-linkedin {
  color: #0d66c2;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-mastodon {
  color: #3288d4;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-medium {
  color: #02ab6c;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-meetup {
  color: #f6405f;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-patreon {
  color: #ff424d;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-pinterest {
  color: #e60122;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-pocket {
  color: #ef4155;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-reddit {
  color: #fe4500;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-skype {
  color: #0478d7;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-snapchat {
  color: #fff;
  stroke: #000;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-soundcloud {
  color: #ff5600;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-spotify {
  color: #1bd760;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-telegram {
  color: #2aabee;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-tiktok {
  color: #000;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-tumblr {
  color: #011835;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-twitch {
  color: #6440a4;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-twitter {
  color: #1da1f2;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-vimeo {
  color: #1eb7ea;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-vk {
  color: #4680c2;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-wordpress {
  color: #3499cd;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-yelp {
  background-color: #d32422;
  color: #fff;
}
.wp-block-social-links.is-style-logos-only .wp-social-link-youtube {
  color: #f00;
}

.wp-block-social-links.is-style-pill-shape .wp-social-link {
  width: auto;
}
.wp-block-social-links.is-style-pill-shape .wp-social-link a,
.wp-block-social-links.is-style-pill-shape .wp-social-link button {
  padding-left: 1.2em;
  padding-right: 1.2em;
}

.wp-block-table {
  overflow-x: auto;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-table table {
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
}
.wp-block-table .has-fixed-layout {
  table-layout: fixed;
  width: 100%;
}
.wp-block-table .has-fixed-layout td,
.wp-block-table .has-fixed-layout th {
  word-break: break-word;
}
.wp-block-table.alignleft, .wp-block-table.aligncenter, .wp-block-table.alignright {
  display: table;
  width: auto;
}
.wp-block-table.alignleft td,
.wp-block-table.alignleft th, .wp-block-table.aligncenter td,
.wp-block-table.aligncenter th, .wp-block-table.alignright td,
.wp-block-table.alignright th {
  word-break: break-word;
}
.wp-block-table .has-subtle-light-gray-background-color tbody td {
  background-color: #f3f4f5;
}
.wp-block-table .has-subtle-pale-green-background-color tbody td {
  background-color: #e9fbe5;
}
.wp-block-table .has-subtle-pale-blue-background-color tbody td {
  background-color: #e7f5fe;
}
.wp-block-table .has-subtle-pale-pink-background-color tbody td {
  background-color: #fcf0ef;
}

.wp-block-tag-cloud {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-tag-cloud .tag-cloud-link {
  display: inline-block;
  margin-right: 5px;
}
.wp-block-tag-cloud .tag-link-count {
  color: var(--cmsmasters-colors-text);
  display: inline-block;
  margin-left: 5px;
  text-decoration: none;
}

.wp-block-text-columns {
  display: flex;
}
.wp-block-text-columns.aligncenter {
  display: flex;
}
.wp-block-text-columns .wp-block-column {
  margin: 0 20px;
  padding: 0;
}
.wp-block-text-columns .wp-block-column:first-child {
  margin-left: 0;
}
.wp-block-text-columns .wp-block-column:last-child {
  margin-right: 0;
}
.wp-block-text-columns.columns-2 .wp-block-column {
  width: 50%;
}
.wp-block-text-columns.columns-3 .wp-block-column {
  width: 33.3333333333%;
}
.wp-block-text-columns.columns-4 .wp-block-column {
  width: 25%;
}

.wp-block-video {
  margin: 0;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.wp-block-video video {
  max-width: 100%;
}
@supports (position: sticky) {
  .wp-block-video [poster] {
    object-fit: cover;
  }
}
.wp-block-video.alignleft {
  margin-left: calc(max(0px, ((100vw - var(--cmsmasters-main-content-width)) / 5)) * -1);
}
.wp-block-column .wp-block-video.alignleft, .cmsmasters-content-layout-l-sidebar .wp-block-video.alignleft {
  margin-left: 0;
}
.wp-block-video.alignright {
  margin-right: calc(max(0px, ((100vw - var(--cmsmasters-main-content-width)) / 5)) * -1);
}
.wp-block-column .wp-block-video.alignright, .cmsmasters-content-layout-r-sidebar .wp-block-video.alignright {
  margin-right: 0;
}
.wp-block-video.aligncenter {
  text-align: center;
}
.wp-block-video figcaption {
  margin-top: 5px;
  margin-bottom: 0;
}