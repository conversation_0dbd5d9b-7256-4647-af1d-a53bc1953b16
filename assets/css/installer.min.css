.merlin__body a:focus{box-shadow:none}.merlin__outer{background-color:#f1f1f1;display:flex;align-items:center;justify-content:center;width:100%;height:100%;position:absolute;left:0;top:0;z-index:1;box-sizing:border-box}.merlin__content:before{content:"\f463";color:#0071a1;font-size:50px;line-height:1em;font-family:dashicons;width:1em;height:1em;margin:auto!important;position:absolute;left:0;right:0;top:0;bottom:50px;z-index:5;opacity:1;transition:opacity .3s ease-in-out;animation-name:spinner;animation-duration:1.2s;animation-fill-mode:both;animation-iteration-count:infinite;animation-timing-function:linear}.loaded .merlin__content:before{opacity:0;transition-duration:.1s}.exiting .merlin__content:before{opacity:1;transition-duration:.3s}.merlin__content--license-key a{margin:auto;top:0;bottom:0}.merlin__body--demos,.merlin__body--demos *{box-sizing:border-box}.merlin__body--demos .merlin__wrapper{max-height:100vh}.merlin__body--demos{display:block;overflow:hidden}.merlin__body--demos .merlin__wrapper{text-align:center;width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;padding:20px}.merlin__body--demos .return-to-dashboard{display:inline-block}.merlin__body--demos .return-to-dashboard.ignore{display:none}.merlin__content--demos{width:100%;max-width:100vw;margin:0 auto;padding:0 0 52px}.cmsmasters-demos-count-1 .merlin__content--demos{max-width:calc((400px + 20px) * 1 + 60px)}.cmsmasters-demos-count-2 .merlin__content--demos{max-width:calc((400px + 20px) * 2 + 60px)}.cmsmasters-demos-count-3 .merlin__content--demos{max-width:calc((400px + 20px) * 3 + 60px)}.merlin__content--demos .dots{top:20px}.cmsmasters-installer-demos{height:100%;overflow-y:auto;opacity:0;transition:opacity .3s ease-in-out}.merlin__body.loaded .cmsmasters-installer-demos{opacity:1}.cmsmasters-installer-demos__list{list-style:none;display:grid;grid-template-columns:repeat(auto-fill,minmax(360px,1fr));grid-gap:20px;margin:0;padding:30px 30px 10px}.cmsmasters-installer-demos__item{border:1px solid #edf0f7;min-width:200px;margin:0;padding:0;overflow:hidden;position:relative;box-shadow:0 0 10px 3px rgba(224,231,243,.5);border-radius:10px}.cmsmasters-installer-demos__item-image{width:100%;height:0;margin:0;padding:0 0 62%;position:relative}.cmsmasters-installer-demos__item-image>span{color:#444;font-size:30px;width:1em;height:1em;margin:auto!important;position:absolute;left:0;right:0;top:0;bottom:0}.cmsmasters-installer-demos__item-image img{display:block;width:auto;max-width:100%;height:auto;max-height:100%;margin:auto;position:absolute;left:0;right:0;top:0;bottom:0;z-index:1}.cmsmasters-installer-demos__item-preview{background-color:rgba(0,0,0,.7);text-decoration:none;display:flex;align-items:center;justify-content:center;width:100%;height:100%;position:absolute;left:0;top:0;z-index:2;opacity:0;transition:opacity .3s ease-in-out}.cmsmasters-installer-demos__item-preview:hover{opacity:1}.cmsmasters-installer-demos__item-preview span{font-size:15px;line-height:1.2em;font-weight:600;background-color:rgba(0,0,0,.7);color:#fff;text-shadow:0 1px 0 rgba(0,0,0,.6);display:block;padding:15px 12px;border-radius:3px}.cmsmasters-installer-demos__item-info{border-top:1px solid #edf0f7;background-color:#fff;padding:15px}.cmsmasters-installer-demos__item-title{color:#252f62;margin:0 0 10px;padding:0;text-align:left}.cmsmasters-installer-demos__item-buttons{display:flex;align-items:center;justify-content:space-between}.cmsmasters-installer-demos__item-buttons a{border:0;outline:none;font-size:12px;line-height:1.7em;font-weight:600;text-decoration:none;box-shadow:none;transition:color .3s ease-in-out}.cmsmasters-installer-demos__item-buttons a.cmsmasters-custom{color:#6b7b9c}.cmsmasters-installer-demos__item-buttons a.cmsmasters-custom:hover{color:#37f}.cmsmasters-installer-demos__item-buttons-express-wrap{position:relative}.cmsmasters-installer-demos__item-buttons-express-wrap label{background-color:#fff;font-size:11px;line-height:1.8em;font-weight:600;display:flex;align-items:center;height:100%;padding:0 10px;position:absolute;left:auto;right:100%;opacity:0;visibility:hidden;white-space:nowrap;transition:all .3s ease-in-out}.cmsmasters-installer-demos__item-buttons-express-wrap label input{margin:0 0 0 .5em}.cmsmasters-installer-demos__item-buttons-express-wrap:hover label{opacity:1;visibility:visible}.cmsmasters-installer-demos__item-buttons a.cmsmasters-express{color:#fff;background-color:#37f;display:block;padding:5px 12px;border-radius:3px;transition:color .3s ease-in-out,background-color .3s ease-in-out}.cmsmasters-installer-demos__item-buttons a.cmsmasters-express:hover{color:#37f;background-color:#ebf2ff}.merlin__select-control-wrapper{display:none}.merlin__body--plugins.no_required_plugins .merlin__button--skip{display:none!important;visibility:hidden!important}.merlin__drawer--install-plugins{display:flex;flex-direction:column;margin-left:-10px;margin-right:-10px;padding-left:10px;padding-right:10px}.merlin__drawer--open .merlin__drawer--install-plugins{overflow-y:auto}.merlin__drawer--install-plugins li{padding-left:5px;padding-right:5px;order:4}.merlin__drawer--install-plugins li[data-slug=cmsmasters-elementor-addon],.merlin__drawer--install-plugins li[data-slug=elementor]{order:1;pointer-events:none}.merlin__drawer--install-plugins li[data-slug=cmsmasters-elementor-addon] label i,.merlin__drawer--install-plugins li[data-slug=elementor] label i{background-color:#7d8183;border-color:#7d8183}.merlin__drawer--install-plugins li[data-slug=advanced-custom-fields-pro],.merlin__drawer--install-plugins li[data-slug=custom-post-type-ui]{order:2}.merlin__drawer--install-plugins li[data-slug=forminator],.merlin__drawer--install-plugins li[data-slug=wpforms-lite]{order:3}.merlin__body.cmsmasters-is-express-install #merlin__drawer-trigger,.merlin__body.cmsmasters-is-express-install .merlin__content--child .merlin__content--transition .merlin__button--knockout{display:none;visibility:hidden}.merlin__body.cmsmasters-is-express-install:not(.js--finished) .merlin__drawer{max-height:400px;margin-top:30px;opacity:1;transform:translateY(0);transition:none}.merlin__body.cmsmasters-is-express-install:not(.js--finished) .merlin__drawer li{opacity:1;transform:translateZ(0);transition:none;pointer-events:none}.merlin__body.cmsmasters-is-express-install .merlin__content__footer{margin-top:2.75em}.merlin__body.cmsmasters-is-express-install .merlin__button--skip,.merlin__body.cmsmasters-is-express-install .merlin__button--skip#close,.merlin__body.cmsmasters-is-express-install .merlin__button--skip#skip{display:none;visibility:hidden}.merlin__body.cmsmasters-is-express-install .merlin__button--next{pointer-events:none;color:transparent;outline:none;border:0;background:none;box-shadow:none;animation:none;transition:none}.merlin__body.cmsmasters-is-express-install .merlin__button--next[data-callback=install_content]{background-color:#f1f1f1;background-image:linear-gradient(45deg,rgba(0,0,0,.035) 25%,transparent 0,transparent 50%,rgba(0,0,0,.035) 0,rgba(0,0,0,.035) 75%,transparent 0,transparent);background-size:20px 20px;animation:progressBarStripes 2s linear infinite}.merlin__body.cmsmasters-is-express-install .merlin__button--proceed:before{content:"\f147";font-family:dashicons;font-size:20px;line-height:1em;background-color:#46b450;color:#fff;display:block;padding:5px;border-radius:50px;width:1em;height:1em;margin:auto!important;position:absolute;left:0;right:0;top:0;bottom:0;animation-name:pulsing;animation-duration:1.2s;animation-fill-mode:both;animation-iteration-count:infinite}.merlin__body.cmsmasters-is-express-install .merlin__button--loading__spinner{opacity:1;transform:scale(1)}.merlin__body.cmsmasters-is-express-install .js-merlin-progress-bar-percentage{display:block!important}.merlin__body.cmsmasters-is-express-install .return-to-dashboard{display:none;visibility:hidden}.cmsmasters-installer-notice{background-color:rgba(0,0,0,.7);width:100%;height:100%;padding:20px;display:flex;align-items:center;justify-content:center;position:fixed;left:0;top:0;z-index:100;visibility:visible;opacity:1;transition:opacity .3s ease-in-out,visibility .3s ease-in-out}.cmsmasters-installer-notice,.cmsmasters-installer-notice *{box-sizing:border-box}.cmsmasters-installer-notice.cmsmasters-installer-notice-hide{opacity:0;visibility:hidden}.cmsmasters-installer-notice__outer{background-color:#0073a9;border-radius:10px;width:540px;max-width:100%;padding:48px 0 0;position:relative;overflow:hidden}.cmsmasters-installer-notice__close{color:#000;background-color:#fff;border:1px solid #fff;font-size:21px;line-height:1em;border-radius:100px;width:28px;height:28px;display:block;position:absolute;top:10px;right:10px;z-index:1;cursor:pointer;transition:color .3s ease-in-out}.cmsmasters-installer-notice__close:hover{color:#0073a9}.cmsmasters-installer-notice__close:before{content:"\f335";font-family:dashicons;display:block;width:1em;height:1em;margin:auto!important;position:absolute;left:0;right:0;top:0;bottom:0}.cmsmasters-installer-notice__inner{text-align:center;font-weight:400;font-size:14px;line-height:1.7em;background-color:#fff;color:#2c2c2c;padding:40px 35px;max-height:calc(100vh - 100px);overflow-y:auto}.cmsmasters-installer-notice__inner>:first-child{margin-top:0}.cmsmasters-installer-notice__inner>:last-child{margin-bottom:0}.cmsmasters-installer-notice__inner a{color:#0073a9;text-decoration:underline;transition:all .3s ease-in-out}.cmsmasters-installer-notice__inner a:hover{text-decoration-color:transparent}.cmsmasters-installer-notice__img{display:flex;justify-content:center;margin-bottom:30px}.cmsmasters-installer-notice__img img{max-width:100%}.cmsmasters-installer-notice__text{margin-bottom:15px}.cmsmasters-installer-notice__text p{margin:0 0 10px}.cmsmasters-installer-notice__text p:last-child{margin-bottom:0}.cmsmasters-installer-notice__title{font-size:15px;font-weight:500;color:#000;margin:0 0 15px}.cmsmasters-installer-notice__list{list-style:none;margin:0 0 15px;padding:0}.cmsmasters-installer-notice__list li{margin:0;padding:0}.cmsmasters-installer-notice__list-grouped{background-color:#f9f9eb;padding:10px}.cmsmasters-installer-notice__list-grouped li{margin-top:10px}.cmsmasters-installer-notice__list-grouped li:first-child{margin-top:0}.cmsmasters-installer-notice__list-separated li{background-color:#f9f9eb;margin-top:5px;padding:10px}.cmsmasters-installer-notice__list-separated li:first-child{margin-top:0}.cmsmasters-installer-notice__info{background-color:#e7f6ff;padding:10px;margin-bottom:15px}.cmsmasters-installer-notice__info p{margin:0 0 10px}.cmsmasters-installer-notice__info p:last-child{margin-bottom:0}.cmsmasters-installer-notice__button{border:0;outline:none;font-size:14px;line-height:1.2em;font-weight:500;background-color:#0073a9;color:#fff;border-radius:5px;padding:8px 15px;overflow:hidden;display:inline-flex;justify-content:center;align-items:center;text-align:center;cursor:pointer;transition:background-color .3s ease-in-out}.cmsmasters-installer-notice__button:hover{background-color:#005d8a}.cmsmasters-installer-notice__button span{display:block}.cmsmasters-installer-notice__button-wrap{margin-bottom:15px}.cmsmasters-installer-exiting-step-message{width:100%;height:50%;padding:30px 33px 35px;position:absolute;left:0;bottom:0;opacity:0;visibility:hidden;transition:opacity .3s ease-in-out,visibility .3s ease-in-out}body.exiting .cmsmasters-installer-exiting-step-message{opacity:1;visibility:visible}.cmsmasters-installer-exiting-step-message,.cmsmasters-installer-exiting-step-message *{box-sizing:border-box}.cmsmasters-installer-exiting-step-message p{margin-bottom:10px}.cmsmasters-installer-exiting-step-message p:last-child{margin-bottom:0}.cmsmasters-merlin-license{margin-top:20px}.cmsmasters-merlin-license input[type=text]{background:transparent;border-radius:3px;box-shadow:inset 0 1px 2px rgba(0,0,0,.06);display:inline-block;font-size:11px;padding:5px 9px;margin:0;width:100%}.cmsmasters-merlin-license__notice{font-size:12px;line-height:1.21em;margin:10px 0 0}.cmsmasters-merlin-license__notice:empty{margin:0}.cmsmasters-merlin-license__notice.error{color:#d90000}.cmsmasters-merlin-license__notice.success{color:#46b450}.cmsmasters-merlin-license__source-code{margin-top:15px}.cmsmasters-merlin-license__source-code label{display:flex;align-items:flex-start;text-align:start;grid-gap:3px;font-size:13px;line-height:1.21em}.cmsmasters-merlin-license__source-code label+label{margin-top:10px}.cmsmasters-merlin-license__source-code input{margin-top:0}.cmsmasters-merlin-license__code{margin-top:15px}.has-error[data-error-field=license_key] .cmsmasters-merlin-license__code input{animation:errorShake .4s linear 1;border-color:#d54e21;box-shadow:0 0 2px rgba(213,78,33,.8)}.cmsmasters-merlin-license__code.cmsmasters-merlin-license--envato-elements-token{display:none}.cmsmasters-merlin-license__code-wrapper{position:relative}.cmsmasters-merlin-license__code-wrapper a{height:27px;position:absolute;right:3px;top:3px;width:27px}.cmsmasters-merlin-license__code-wrapper a:after{background:transparent;background:linear-gradient(270deg,#fff 40%,hsla(0,0%,100%,0));border-radius:3px;content:"";height:27px;pointer-events:none;position:absolute;right:23px;top:0;width:20px;z-index:1}.cmsmasters-merlin-license__code-wrapper a:active svg{animation:small_pulse .2s;animation-timing-function:cubic-bezier(.694,0,.335,1);animation-fill-mode:none}.cmsmasters-merlin-license__code-wrapper .icon{height:27px;margin-bottom:0;opacity:.6;transform:translateZ(0);transition:fill .15s cubic-bezier(.694,0,.335,1),opacity .15s cubic-bezier(.694,0,.335,1);width:27px;z-index:9999;backface-visibility:hidden;fill:#7d8183}@media screen and (prefers-reduced-motion:reduce){.cmsmasters-merlin-license__code-wrapper .icon{transition:fill 50ms ease-in-out,opacity 50ms ease-in-out}}.cmsmasters-merlin-license__code-wrapper .icon:hover{opacity:1;fill:#0073aa}.cmsmasters-merlin-license__code-description{font-size:11px;line-height:1.21em;text-align:left;display:block}.cmsmasters-merlin-license__code-description-top{margin-bottom:7px}.cmsmasters-merlin-license__code-description-bottom{margin-top:7px}.cmsmasters-merlin-license__user-info{margin-top:30px}.has-error[data-error-field=email] .cmsmasters-merlin-license__user-info input[name=cmsmasters_merlin_license__user_email]{animation:errorShake .4s linear 1;border-color:#d54e21;box-shadow:0 0 2px rgba(213,78,33,.8)}.cmsmasters-merlin-license__user-info--title{font-size:20px;line-height:1.21em;margin:0 0 15px}.cmsmasters-merlin-license__user-info--text{font-size:13px;line-height:1.21em;margin:0}.cmsmasters-merlin-license__user-info--item{margin-top:7px}.cmsmasters-merlin-license__user-info--privacy{font-size:11px;line-height:1.21em;margin:7px 0 0;padding:0 5px}@keyframes spinner{0%{transform:rotate(0deg) scale(1)}50%{transform:rotate(180deg) scale(1.2)}to{transform:rotate(1turn) scale(1)}}@keyframes pulsing{0%{transform:scale(1)}50%{transform:scale(1.1)}to{transform:scale(1)}}@keyframes progressBarStripes{0%{background-position:20px 0}to{background-position:0 0}}