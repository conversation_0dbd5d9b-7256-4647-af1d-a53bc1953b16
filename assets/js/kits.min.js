(()=>{var e={266:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},983:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},81:(e,t,r)=>{var o=r(40);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}e.exports=function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},910:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function _getPrototypeOf(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},724:(e,t,r)=>{var o=r(196);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},173:(e,t,r)=>{var o=r(501).default,n=r(266);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"===o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},196:e=>{function _setPrototypeOf(t,r){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,r)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},27:(e,t,r)=>{var o=r(501).default;e.exports=function _toPrimitive(e,t){if("object"!==o(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},40:(e,t,r)=>{var o=r(501).default,n=r(27);e.exports=function _toPropertyKey(e){var t=n(e,"string");return"symbol"===o(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(203),t=e(__webpack_require__(983)),r=e(__webpack_require__(81)),o=e(__webpack_require__(724)),n=e(__webpack_require__(173)),s=e(__webpack_require__(910));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var r,o=(0,s.default)(e);if(t){var i=(0,s.default)(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return(0,n.default)(this,r)}}new(function(e){(0,o.default)(Kits,e);var n=_createSuper(Kits);function Kits(){return(0,t.default)(this,Kits),n.apply(this,arguments)}return(0,r.default)(Kits,[{key:"onElementorLoaded",value:function onElementorLoaded(){elementor.on("document:loaded",this.initGroups.bind(this)),$e.routes.on("run:after",(function(e,t){var r=elementor.$previewContents[0].body.querySelector(".cmsmasters-page-preloader");r&&(r.classList.remove("cmsmasters-page-preloader--preview"),"panel/global/cmsmasters-theme-page-preloader"===t&&r.classList.add("cmsmasters-page-preloader--preview"))}))}},{key:"initGroups",value:function initGroups(){this.kitPanelMenu=elementor.getPanelView().getPages().kit_menu.view,this.kitTabs=$e.components.get("panel/global").tabs,Object.keys(this.kitTabs).length&&(this.groupsCollectionArgs=[],this.addKitGroup("global","design_system"),this.addKitGroup("cmsmasters-theme",!1,!0),this.initSettingsGroup(),this.kitPanelMenu.groups=new Backbone.Collection(this.groupsCollectionArgs))}},{key:"addKitGroup",value:function addKitGroup(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=t||e.split("-").join("_"),s=elementor.translate(n);r&&(s+=' <i class="elementor-panel-menu-group-title-icon cmsms-logo"></i>');var i=o||this.getKitGroupTabs(e);this.groupsCollectionArgs.push({name:n,title:s,items:i})}},{key:"getKitGroupTabs",value:function getKitGroupTabs(e){return this.kitPanelMenu.createGroupItems(e)}},{key:"initSettingsGroup",value:function initSettingsGroup(){var e=this.getKitGroupTabs("settings");for(var t in e.push({name:"settings-additional-settings",icon:"eicon-tools",title:elementor.translate("additional_settings"),type:"link",link:elementor.config.admin_settings_url,newTab:!0}),e)"settings-background"===e[t].name&&e.splice(t,1);this.addKitGroup("settings",!1,!1,e)}}]),Kits}(elementorModules.editor.utils.Module))})()})();
//# sourceMappingURL=kits.min.js.map