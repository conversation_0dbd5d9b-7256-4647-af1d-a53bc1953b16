(()=>{"use strict";var e={601:()=>{jQuery(".cmsmasters-dismiss-notice-permanent").on("click",".notice-dismiss",(function(){var e=jQuery(this).closest(".cmsmasters-dismiss-notice-permanent").data("optionKey"),s={action:"cmsmasters_hide_admin_notice",nonce:cmsmasters_admin.nonce,option_key:e};jQuery.post(ajaxurl,s)}))}},s={};function __webpack_require__(r){var t=s[r];if(void 0!==t)return t.exports;var i=s[r]={exports:{}};return e[r](i,i.exports,__webpack_require__),i.exports}__webpack_require__(601)})();
//# sourceMappingURL=admin.min.js.map