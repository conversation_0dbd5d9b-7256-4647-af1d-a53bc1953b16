(()=>{"use strict";var e={784:()=>{jQuery(".cmsmasters-install-button").on("click",(function(e){e.preventDefault();var s=this.getAttribute("href"),t=this.getAttribute("data-key"),r="custom",a="custom";jQuery(this).hasClass("cmsmasters-express")&&(r="express",a=jQuery(this).closest(".cmsmasters-installer-demos__item").find(".cmsmasters-import-content-status")[0].checked?"enabled":"disabled");var i={action:"cmsmasters_installer",wpnonce:installer_params.wpnonce,type:r,content_import:a,demo_key:t};jQuery.post(installer_params.ajaxurl,i,(function(){window.location=s}))})),"express"===installer_params.type&&(jQuery(".merlin__body").addClass("cmsmasters-is-express-install"),setTimeout((function(){jQuery(".merlin__button--next").trigger("click")}),500))},247:()=>{jQuery(".cmsmasters-installer-notice__close-js").on("click",(function(){jQuery(this).closest(".cmsmasters-installer-notice").addClass("cmsmasters-installer-notice-hide")}))}},s={};function __webpack_require__(t){var r=s[t];if(void 0!==r)return r.exports;var a=s[t]={exports:{}};return e[t](a,a.exports,__webpack_require__),a.exports}jQuery(document).ready((function(){__webpack_require__(247),__webpack_require__(784)}))})();
//# sourceMappingURL=installer.min.js.map