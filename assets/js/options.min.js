(()=>{var e={310:(e,s,t)=>{"use strict";var a=t(203)(t(501));!function cmsmastersOptionsDemos(){var e=jQuery(".cmsmasters-demo-apply-button"),s=jQuery(".cmsmasters-options-demos-notice__button"),t=jQuery(".cmsmasters-options-demos__item"),n={init:function init(){n.bindEvents()},bindEvents:function bindEvents(){e.on("click",n.runAjax),s.on("click",n.runReload)},runAjax:function runAjax(e){if(!confirm(cmsmasters_options.apply_demo_question))return!1;e.preventDefault(),n.$button=jQuery(this),n.$item=jQuery(this).closest(".cmsmasters-options-demos__item"),n.$notice=jQuery(".cmsmasters-options-demos-notice"),n.$notice_message=n.$notice.find(".cmsmasters-options-demos-notice__message"),n.ajaxData={nonce:cmsmasters_options.nonce,action:"cmsmasters_apply_demo",demo_key:n.$button.data("demoKey")},n.$button.addClass("cmsmasters-loading"),n.doAjax()},doAjax:function doAjax(){jQuery.post(ajaxurl,n.ajaxData,n.ajaxCallback).fail(n.ajaxCallback)},ajaxCallback:function ajaxCallback(e){var s="cmsmasters-active",o=!0;void 0!==(0,a.default)(e.success)&&e.success&&(o=!1),s+=o?" cmsmasters-error":" cmsmasters-success",setTimeout((function(){n.$notice.addClass(s),n.$notice_message.html(e.message),o?n.$button.removeClass("cmsmasters-loading"):(t.removeClass("cmsmasters-active"),n.$item.addClass("cmsmasters-active"))}),1e3)},runReload:function runReload(){jQuery(this).addClass("cmsmasters-loading"),location.reload()}};n.init()}()},121:()=>{"use strict";jQuery(".cmsmasters-options-notice").each((function(){var e=jQuery(this).data("option");jQuery("#"+e).parent().addClass("cmsmasters-error")}))},820:(e,s,t)=>{"use strict";var a=t(203)(t(501));!function cmsmastersOptionsLicenseData(){var e=jQuery(".cmsmasters-options .button[data-action=update-license-data]"),s={init:function init(){e.on("click",(function(e){e.preventDefault(),s.$button=jQuery(this),s.$notice=s.$button.parent().find(".cmsmasters-notice"),s.ajaxData={nonce:cmsmasters_options.nonce,action:"cmsmasters_update_license_data",user_name:jQuery("input[name=cmsmasters_options_license_data__user_name]").val(),user_email:jQuery("input[name=cmsmasters_options_license_data__user_email]").val()},s.$button.addClass("cmsmasters-loading"),s.doAjax()}))},doAjax:function doAjax(){jQuery.post(ajaxurl,s.ajaxData,s.ajaxCallback).fail(s.ajaxCallback)},ajaxCallback:function ajaxCallback(e){var t="cmsmasters-active",n=!0;void 0!==(0,a.default)(e.success)&&e.success&&(n=!1),t+=n?" cmsmasters-error":" cmsmasters-success",setTimeout((function(){s.$notice.addClass(t),s.$notice.html(e.message),n&&s.$button.removeClass("cmsmasters-loading")}),1e3),n||s.$button.removeClass("cmsmasters-loading").attr("disabled","disabled")}};s.init()}()},426:(e,s,t)=>{"use strict";var a=t(203)(t(501));!function cmsmastersOptionsLicense(){jQuery("input[name=cmsmasters_options_license__source_code]").on("change",(function(){var e=jQuery(this).val();jQuery(".cmsmasters-options-license__code").slideUp("fast"),jQuery(".cmsmasters-options-license__code.cmsmasters-options-license--".concat(e)).slideDown("fast")}));var e=jQuery(".cmsmasters-options .button[data-license]"),s={init:function init(){e.on("click",(function(e){e.preventDefault(),s.$button=jQuery(this),s.$notice=s.$button.parent().find(".cmsmasters-notice"),s.ajaxData={nonce:cmsmasters_options.nonce},"activate"===s.$button.data("license")?(s.ajaxData.action="cmsmasters_activate_license",s.ajaxData.user_name=jQuery("input[name=cmsmasters_options_license__user_name]").val(),s.ajaxData.user_email=jQuery("input[name=cmsmasters_options_license__user_email]").val(),s.ajaxData.source_code=jQuery("input[name=cmsmasters_options_license__source_code]:checked").val(),s.ajaxData.purchase_code=jQuery("input[name=cmsmasters_options_license__purchase_code]").val(),s.ajaxData.envato_elements_token=jQuery("input[name=cmsmasters_options_license__envato_elements_token]").val()):"deactivate"===s.$button.data("license")&&(s.ajaxData.action="cmsmasters_deactivate_license"),s.$button.addClass("cmsmasters-loading"),s.doAjax()}))},doAjax:function doAjax(){jQuery.post(ajaxurl,s.ajaxData,s.ajaxCallback).fail(s.ajaxCallback)},ajaxCallback:function ajaxCallback(e){var t="cmsmasters-active",n=!0;void 0!==(0,a.default)(e.success)&&e.success&&(n=!1),t+=n?" cmsmasters-error":" cmsmasters-success",setTimeout((function(){s.$notice.addClass(t),s.$notice.html(e.message),n&&s.$button.removeClass("cmsmasters-loading")}),1e3),n||location.reload(!0)}};s.init()}()},324:()=>{"use strict";!function cmsmastersSections(){var e=jQuery(".cmsmasters-options-form"),s=jQuery(".cmsmasters-options-tabs-nav"),t={init:function init(){t.setElements(),t.goToSectionFromHash(),t.bindEvents()},setElements:function setElements(){t.$sections=e.find(".cmsmasters-options-section"),t.$activeSection=t.$sections.filter(".cmsmasters-active"),t.$links=s.children(),t.$activeLink=t.$links.filter(".nav-tab-active")},bindEvents:function bindEvents(){t.$links.on({click:function click(e){e.preventDefault(),e.currentTarget.focus()},focus:function focus(){var e=location.href.replace(/#.*/,"");history.pushState({},"",e+this.hash),t.goToSectionFromHash()}})},goToSectionFromHash:function goToSectionFromHash(){var e=location.hash.slice(1);e&&t.goToSection(e)},goToSection:function goToSection(s){var a=t.$sections;if(a.length){var n=a.filter("#"+s),o=t.$links.filter("#"+s+"-link");t.$activeSection.removeClass("cmsmasters-active"),t.$activeLink.removeClass("nav-tab-active"),n.addClass("cmsmasters-active"),o.addClass("nav-tab-active"),e.attr("action","options.php#"+s),t.$activeSection=n,t.$activeLink=o}}};t.init()}()},203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},501:e=>{function _typeof(s){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(s)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports}},s={};function __webpack_require__(t){var a=s[t];if(void 0!==a)return a.exports;var n=s[t]={exports:{}};return e[t](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";__webpack_require__(310),__webpack_require__(121),__webpack_require__(426),__webpack_require__(820),__webpack_require__(324)})()})();
//# sourceMappingURL=options.min.js.map