(()=>{"use strict";var e={765:()=>{jQuery('.acf-field-select[data-name="cmsmasters_layout"] select').on("change",(function(){var e=jQuery(this).val(),r=jQuery("body");r.removeClass((function(e,r){return(r.match(/cmsmasters-layout-\S+/g)||[]).join(" ")})),r.addClass((function(r,t){if("default"===e){var a=t.match(/cmsmasters-def-layout-(\S+)\s+/);return t+" cmsmasters-layout-"+a[1]}return t+" cmsmasters-layout-"+e}))}))},341:()=>{wp.domReady((function(){wp.blocks.unregisterBlockStyle("core/quote",["default","large"]),wp.blocks.unregisterBlockStyle("core/table",["default","stripes"])}))}},r={};function __webpack_require__(t){var a=r[t];if(void 0!==a)return a.exports;var s=r[t]={exports:{}};return e[t](s,s.exports,__webpack_require__),s.exports}jQuery(document).ready((function(){__webpack_require__(765)})),__webpack_require__(341)})();
//# sourceMappingURL=gutenberg.min.js.map