/*! For license information please see frontend.min.js.LICENSE.txt */
(()=>{var e={801:(e,t,r)=>{"use strict";var n=r(203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=n(r(983)),a=n(r(81)),o=function(){function cmsmastersAssetsLoader(){(0,s.default)(this,cmsmastersAssetsLoader)}return(0,a.default)(cmsmastersAssetsLoader,[{key:"getScriptElement",value:function getScriptElement(e){var t=document.createElement("script");return t.src=e,t}},{key:"getStyleElement",value:function getStyleElement(e){var t=document.createElement("link");return t.rel="stylesheet",t.href=e,t}},{key:"load",value:function load(e,t){var r=this,n=cmsmasters_localize_vars.assets_data[e][t];return n.loader||(n.loader=new Promise((function(t){var s="style"===e?r.getStyleElement(n.src):r.getScriptElement(n.src);s.onload=function(){return t(!0)};var a="head"===n.parent?n.parent:"body";document[a].appendChild(s)}))),n.loader}}]),cmsmastersAssetsLoader}();t.default=o},283:()=>{"use strict";"ontouchstart"in document.documentElement&&jQuery("body").addClass("cmsmasters-is-touch")},122:()=>{"use strict";jQuery(".cmsmasters-header-search-button-toggle").on("click",(function(){jQuery(".cmsmasters-header-search-form").addClass("cmsmasters-show"),jQuery(".cmsmasters-header-search-form").find("input[type=search]").focus()})),jQuery(".cmsmasters-header-search-form__close").on("click",(function(){jQuery(".cmsmasters-header-search-form").removeClass("cmsmasters-show")}))},803:(e,t,r)=>{"use strict";var n=r(203)(r(211));jQuery(".cmsmasters-header-top-toggle__inner").on("click",(function(){var e=jQuery(this),t=jQuery(".cmsmasters-header-top__outer");e.hasClass("cmsmasters-active")?(t.slideUp(),e.removeClass("cmsmasters-active")):(t.slideDown(),e.addClass("cmsmasters-active"))})),jQuery(window).on("resize",(function(){(0,n.default)()>cmsmasters_localize_vars.mobile_max_breakpoint&&(jQuery(".cmsmasters-header-top-toggle__inner").removeClass("cmsmasters-active"),jQuery(".cmsmasters-header-top__outer").css("display",""))}))},248:()=>{"use strict";jQuery.fn.cmsmastersMasonryGrid=function(e){var t=this;if(!(t.length<1)){var r={itemClass:".cmsmasters-archive-post"},n={};(n={init:function init(){n.options=jQuery.extend({},r,e),n.container=t,n.items=n.container.find(n.options.itemClass),document.addEventListener("cmsmasters_customize_change_css_var",(function(){setTimeout((function(){n.run()}))})),n.container.imagesLoaded((function(){n.run()})),jQuery(window).on("resize",(function(){setTimeout((function(){n.run()}),300)}))},getColumns:function getColumns(){for(var e=n.container.css("grid-template-columns"),t=1,r=0;r<e.length;t+=+(" "===e[r++]));return t},run:function run(){var e=[],t=n.container.position().top+parseInt(n.container.css("margin-top"),10),r=n.getColumns(),s=parseInt(n.container.css("grid-row-gap"),10);n.items.removeAttr("style"),n.items.each((function(n){var a=Math.floor(n/r),o=jQuery(this),i=o[0].getBoundingClientRect().height+s;if(a){var c=o.position(),u=n%r,l=c.top-t-e[u];l-=parseInt(o.css("margin-top"),10),l*=-1,o.css("margin-top",l+"px"),e[u]+=i}else e.push(i)}))}}).init()}},jQuery(".cmsmasters-archive.cmsmasters-grid.cmsmasters-masonry").cmsmastersMasonryGrid()},211:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function cmsmastersMediaWidth(){return parseInt(jQuery(".cmsmasters-responsive-width").css("width"))}},9:(e,t,r)=>{"use strict";var n=r(203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=n(r(983)),a=n(r(81)),o=function(){function cmsmastersPagePreloader(){(0,s.default)(this,cmsmastersPagePreloader),this.$container=document.querySelector(".cmsmasters-page-preloader"),this.$container&&(this.classes=this.getClasses(),this.bindEvents())}return(0,a.default)(cmsmastersPagePreloader,[{key:"getClasses",value:function getClasses(){return{entering:"cmsmasters-page-preloader--entering",entered:"cmsmasters-page-preloader--entered",exiting:"cmsmasters-page-preloader--exiting",preview:"cmsmasters-page-preloader--preview"}}},{key:"bindEvents",value:function bindEvents(){window.addEventListener("pageshow",this.onPageShow.bind(this)),window.addEventListener("beforeunload",this.onPageBeforeUnload.bind(this))}},{key:"onPageShow",value:function onPageShow(){var e=this;this.$container.classList.contains(this.classes.exiting)&&(this.$container.classList.add(this.classes.entered),this.$container.classList.remove(this.classes.exiting)),this.animateState("entering").then((function(){e.$container.classList.add(e.classes.entered)}))}},{key:"onPageBeforeUnload",value:function onPageBeforeUnload(){var e=this;this.$container.classList.remove(this.classes.entered),this.animateState("exiting").then((function(){e.$container.classList.add(e.classes.exiting)}))}},{key:"animateState",value:function animateState(e){var t,r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=null===(t=this.classes)||void 0===t?void 0:t[e];if(!s)return new Promise((function(t,r){r(e)}));this.$container.classList.remove(s),this.$container.classList.add(s);var a=parseInt(this.getCssVar("--cmsmasters-page-preloader-animation-duration"))||0;return new Promise((function(t){setTimeout((function(){r.$container.classList.remove(s),t(e)}),a+n)}))}},{key:"getCssVar",value:function getCssVar(e){return window.getComputedStyle(document.documentElement).getPropertyValue(e)}}]),cmsmastersPagePreloader}();t.default=o,new o},346:(e,t,r)=>{"use strict";var n=r(203)(r(211));jQuery.fn.cmsmastersResponsiveNav=function(e){var t={submenu:"ul.sub-menu, ul.children",respButton:".cmsmasters-burger-menu-button__toggle",startWidth:cmsmasters_localize_vars.tablet_breakpoint},r=this,s={init:function init(){s.options=jQuery.extend({},t,e),s.el=r,s.params={},s.params.subLinkToggle=void 0,s.setVars(),s.restartNav()},setVars:function setVars(){s.params.parentNav=s.el.closest(".cmsmasters-menu"),s.params.submenu=s.el.find(s.options.submenu),s.params.subLink=s.params.submenu.closest("li").find("> a"),s.params.subText=s.params.submenu.closest("li").find("> a:not([href])"),s.params.respButton=jQuery(s.options.respButton),s.params.startWidth=s.options.startWidth,s.params.win=jQuery(window),s.params.trigger=!1,s.params.counter=0,s.startEvent(),s.params.subLink.each((function(){jQuery(this).addClass("cmsmasters-has-child-indicator").find(".cmsmasters-menu__item").append('<span class="cmsmasters-child-indicator cmsmasters-theme-icon-nav-arrow"></span>')}))},buildNav:function buildNav(){s.params.trigger=!0,s.params.counter=1,s.params.subLinkToggle=s.params.subLink.find(".cmsmasters-child-indicator"),s.params.submenu.hide(),s.attachEvents()},restartNav:function restartNav(){!s.params.trigger&&(0,n.default)()<s.params.startWidth?s.buildNav():s.params.trigger&&(0,n.default)()>=s.params.startWidth&&s.destroyNav()},resetNav:function resetNav(){s.params.subLinkToggle.removeClass("cmsmasters-active"),s.params.submenu.hide()},destroyNav:function destroyNav(){s.params.subLink.each((function(){jQuery(this).find(".cmsmasters-menu__item").find(".cmsmasters-child-indicator").removeClass("cmsmasters-active")})),s.params.submenu.css("display",""),s.params.respButton.removeClass("cmsmasters-active"),s.params.parentNav.css("display",""),s.params.trigger=!1,s.detachEvents()},startEvent:function startEvent(){s.params.win.on("resize",(function(){s.restartNav()}))},attachEvents:function attachEvents(){s.params.subLinkToggle.on("click",(function(){return jQuery(this).hasClass("cmsmasters-active")?(jQuery(this).removeClass("cmsmasters-active").closest("li").find("ul.sub-menu, ul.children").hide(),jQuery(this).closest("li").find("span.cmsmasters-child-indicator").removeClass("cmsmasters-active")):(jQuery(this).addClass("cmsmasters-active"),jQuery(this).closest("li").find("> ul.sub-menu, > ul.children").show()),!1})),s.params.subText.on("click",(function(){jQuery(this).find("span.cmsmasters-child-indicator").trigger("click")})),s.params.respButton.on("click",(function(){return s.params.trigger&&jQuery(this).hasClass("cmsmasters-active")&&s.resetNav(),jQuery(this).is(":not(.cmsmasters-active)")?(s.params.parentNav.css({display:"block"}),jQuery(this).addClass("cmsmasters-active")):(s.params.parentNav.css({display:"none"}),jQuery(this).removeClass("cmsmasters-active")),!1}))},detachEvents:function detachEvents(){s.params.subLinkToggle.off("click"),s.params.respButton.off("click")}};s.init()},jQuery(".cmsmasters-header-top-menu__list").cmsmastersResponsiveNav({respButton:".cmsmasters-header-top-burger-menu-button__toggle"}),jQuery(".cmsmasters-header-mid-menu__list").cmsmastersResponsiveNav({respButton:".cmsmasters-header-mid-burger-menu-button__toggle"}),jQuery(".cmsmasters-header-bot-menu__list").cmsmastersResponsiveNav({respButton:".cmsmasters-header-bot-burger-menu-button__toggle"})},911:(e,t,r)=>{"use strict";var n=r(203),s=n(r(824)),a=n(r(29)),o=n(r(801));jQuery.fn.cmsmastersSwiperSlider=function(){var e,t=this,r=".cmsmasters-swiper",n={loop:!1,pagination:{clickable:!0},autoHeight:!0},i={};i={init:(e=(0,a.default)(s.default.mark((function _callee(){var e;return s.default.wrap((function _callee$(s){for(;;)switch(s.prev=s.next){case 0:if(i.container="#".concat(t.attr("id")," ").concat(r,"__container"),i.options=jQuery(t).data("options"),"none"!==i.options.pagination&&(n.pagination.el=jQuery(t).find("".concat(r,"__pagination-items")).get(0),n.pagination.type=i.options.pagination,"bullets"===i.options.pagination&&("dynamic"===i.options.bullets_type?n.pagination.dynamicBullets=!0:"numbered"===i.options.bullets_type&&(n.pagination.renderBullet=function(e,t){return'<span class="'.concat(t,'">').concat(e+1,"</span>")}))),!0===i.options.arrows&&(n.navigation={nextEl:jQuery(t).find("".concat(r,"__button.cmsmasters-next")).get(0),prevEl:jQuery(t).find("".concat(r,"__button.cmsmasters-prev")).get(0)}),i.settings=jQuery.extend({},n,t.data("settings")),window.Swiper){s.next=9;break}return e=new o.default,s.next=9,e.load("script","swiper");case 9:i.run_slider();case 10:case"end":return s.stop()}}),_callee)}))),function init(){return e.apply(this,arguments)}),run_slider:function run_slider(){var e=new Swiper(i.container,i.settings);!0===i.options.pause_on_hover&&jQuery(i.container).on("mouseenter",(function(){e.autoplay.stop()})).on("mouseleave",(function(){e.autoplay.start()})),document.addEventListener("cmsmasters_customize_change_css_var",(function(){setTimeout((function(){e.update()}))}))}},i.init()},jQuery(".cmsmasters-swiper").each((function(){jQuery(this).cmsmastersSwiperSlider()}))},29:e=>{function asyncGeneratorStep(e,t,r,n,s,a,o){try{var i=e[a](o),c=i.value}catch(e){return void r(e)}i.done?t(c):Promise.resolve(c).then(n,s)}e.exports=function _asyncToGenerator(e){return function(){var t=this,r=arguments;return new Promise((function(n,s){var a=e.apply(t,r);function _next(e){asyncGeneratorStep(a,n,s,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(a,n,s,_next,_throw,"throw",e)}_next(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},983:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},81:(e,t,r)=>{var n=r(40);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,n(s.key),s)}}e.exports=function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},337:(e,t,r)=>{var n=r(501).default;function _regeneratorRuntime(){"use strict";e.exports=_regeneratorRuntime=function _regeneratorRuntime(){return t},e.exports.__esModule=!0,e.exports.default=e.exports;var t={},r=Object.prototype,s=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function define(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{define({},"")}catch(e){define=function define(e,t,r){return e[t]=r}}function wrap(e,t,r,n){var s=t&&t.prototype instanceof Generator?t:Generator,o=Object.create(s.prototype),i=new Context(n||[]);return a(o,"_invoke",{value:makeInvokeMethod(e,r,i)}),o}function tryCatch(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=wrap;var l={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var m={};define(m,i,(function(){return this}));var d=Object.getPrototypeOf,p=d&&d(d(values([])));p&&p!==r&&s.call(p,i)&&(m=p);var f=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(m);function defineIteratorMethods(e){["next","throw","return"].forEach((function(t){define(e,t,(function(e){return this._invoke(t,e)}))}))}function AsyncIterator(e,t){function invoke(r,a,o,i){var c=tryCatch(e[r],e,a);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==n(l)&&s.call(l,"__await")?t.resolve(l.__await).then((function(e){invoke("next",e,o,i)}),(function(e){invoke("throw",e,o,i)})):t.resolve(l).then((function(e){u.value=e,o(u)}),(function(e){return invoke("throw",e,o,i)}))}i(c.arg)}var r;a(this,"_invoke",{value:function value(e,n){function callInvokeWithMethodAndArg(){return new t((function(t,r){invoke(e,n,t,r)}))}return r=r?r.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(e,t,r){var n="suspendedStart";return function(s,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===s)throw a;return doneResult()}for(r.method=s,r.arg=a;;){var o=r.delegate;if(o){var i=maybeInvokeDelegate(o,r);if(i){if(i===l)continue;return i}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=tryCatch(e,t,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===l)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function maybeInvokeDelegate(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,maybeInvokeDelegate(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),l;var s=tryCatch(n,e.iterator,t.arg);if("throw"===s.type)return t.method="throw",t.arg=s.arg,t.delegate=null,l;var a=s.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,l):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,l)}function pushTryEntry(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function resetTryEntry(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function Context(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(pushTryEntry,this),this.reset(!0)}function values(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function next(){for(;++r<e.length;)if(s.call(e,r))return next.value=e[r],next.done=!1,next;return next.value=void 0,next.done=!0,next};return n.next=n}}return{next:doneResult}}function doneResult(){return{value:void 0,done:!0}}return GeneratorFunction.prototype=GeneratorFunctionPrototype,a(f,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),a(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===GeneratorFunction||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,define(e,u,"GeneratorFunction")),e.prototype=Object.create(f),e},t.awrap=function(e){return{__await:e}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,c,(function(){return this})),t.AsyncIterator=AsyncIterator,t.async=function(e,r,n,s,a){void 0===a&&(a=Promise);var o=new AsyncIterator(wrap(e,r,n,s),a);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},defineIteratorMethods(f),define(f,u,"Generator"),define(f,i,(function(){return this})),define(f,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function next(){for(;r.length;){var e=r.pop();if(e in t)return next.value=e,next.done=!1,next}return next.done=!0,next}},t.values=values,Context.prototype={constructor:Context,reset:function reset(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(resetTryEntry),!e)for(var t in this)"t"===t.charAt(0)&&s.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function stop(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function dispatchException(e){if(this.done)throw e;var t=this;function handle(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r],a=n.completion;if("root"===n.tryLoc)return handle("end");if(n.tryLoc<=this.prev){var o=s.call(n,"catchLoc"),i=s.call(n,"finallyLoc");if(o&&i){if(this.prev<n.catchLoc)return handle(n.catchLoc,!0);if(this.prev<n.finallyLoc)return handle(n.finallyLoc)}else if(o){if(this.prev<n.catchLoc)return handle(n.catchLoc,!0)}else{if(!i)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return handle(n.finallyLoc)}}}},abrupt:function abrupt(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&s.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,l):this.complete(o)},complete:function complete(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),l},finish:function finish(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),resetTryEntry(r),l}},catch:function _catch(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var s=n.arg;resetTryEntry(r)}return s}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(e,t,r){return this.delegate={iterator:values(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}e.exports=_regeneratorRuntime,e.exports.__esModule=!0,e.exports.default=e.exports},27:(e,t,r)=>{var n=r(501).default;e.exports=function _toPrimitive(e,t){if("object"!==n(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!==n(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},40:(e,t,r)=>{var n=r(501).default,s=r(27);e.exports=function _toPropertyKey(e){var t=s(e,"string");return"symbol"===n(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},824:(e,t,r)=>{var n=r(337)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,__webpack_require__),s.exports}(()=>{"use strict";__webpack_require__(801),__webpack_require__(283),__webpack_require__(211),__webpack_require__(9),__webpack_require__(248),__webpack_require__(346),__webpack_require__(803),__webpack_require__(122),__webpack_require__(911)})()})();
//# sourceMappingURL=frontend.min.js.map