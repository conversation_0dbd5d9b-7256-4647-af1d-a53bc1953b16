<?php
namespace FaithConnectSpace\Kits\Settings\SidebarWidgets;

use FaithConnectSpace\Kits\Settings\Base\Settings_Tab_Base;

use Elementor\Controls_Manager;


if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}


/**
 * Sidebar Widgets Title settings.
 */
class Title extends Settings_Tab_Base {

	/**
	 * Get toggle name.
	 *
	 * Retrieve the toggle name.
	 *
	 * @return string Toggle name.
	 */
	public static function get_toggle_name() {
		return 'sidebar_widgets_title';
	}

	/**
	 * Get title.
	 *
	 * Retrieve the toggle title.
	 */
	public function get_title() {
		return esc_html__( 'Widget Title', 'faith-connect' );
	}

	/**
	 * Get control ID prefix.
	 *
	 * Retrieve the control ID prefix.
	 *
	 * @return string Control ID prefix.
	 */
	protected static function get_control_id_prefix() {
		$toggle_name = self::get_toggle_name();

		return parent::get_control_id_prefix() . "_{$toggle_name}";
	}

	/**
	 * Register toggle controls.
	 *
	 * Registers the controls of the kit settings tab toggle.
	 */
	protected function register_toggle_controls() {
		$this->add_var_group_control( '', self::VAR_TYPOGRAPHY );

		$this->add_control(
			'colors_heading_control',
			array(
				'label' => esc_html__( 'Colors', 'faith-connect' ),
				'type' => Controls_Manager::HEADING,
			)
		);

		$this->add_control(
			'colors_color',
			array(
				'label' => esc_html__( 'Color', 'faith-connect' ),
				'type' => Controls_Manager::COLOR,
				'dynamic' => array(),
				'selectors' => array(
					':root' => '--' . $this->get_control_prefix_parameter( '', 'colors_color' ) . ': {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'colors_link',
			array(
				'label' => esc_html__( 'Link', 'faith-connect' ),
				'type' => Controls_Manager::COLOR,
				'dynamic' => array(),
				'selectors' => array(
					':root' => '--' . $this->get_control_prefix_parameter( '', 'colors_link' ) . ': {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'colors_hover',
			array(
				'label' => esc_html__( 'Link Hover', 'faith-connect' ),
				'type' => Controls_Manager::COLOR,
				'dynamic' => array(),
				'selectors' => array(
					':root' => '--' . $this->get_control_prefix_parameter( '', 'colors_hover' ) . ': {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'box_heading_control',
			array(
				'label' => esc_html__( 'Container', 'faith-connect' ),
				'type' => Controls_Manager::HEADING,
				'separator' => 'before',
			)
		);

		$this->add_controls_group( 'box', self::CONTROLS_CONTAINER_BOX, array(
			'popover' => false,
		) );
	}

}
