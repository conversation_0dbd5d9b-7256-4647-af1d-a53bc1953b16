var Merlin=function(e){var n={install_child:function(e){(new t).init(e)},activate_license:function(e){(new s).init(e)},install_plugins:function(e){(new r).init(e)},install_content:function(e){(new o).init(e)}};function i(){var i=e(".merlin__body"),t=(e(".merlin__body--loading"),e(".merlin__body--exiting"),e("#merlin__drawer-trigger"));drawer_opened="merlin__drawer--open",setTimeout((function(){i.addClass("loaded")}),100),t.on("click",(function(){i.toggleClass(drawer_opened)})),e(".merlin__button--proceed:not(.merlin__button--closer)").on("click",(function(e){e.preventDefault();var n=this.getAttribute("href");i.addClass("exiting"),setTimeout((function(){window.location=n}),400)})),e(".merlin__button--closer").on("click",(function(e){i.removeClass(drawer_opened),e.preventDefault();var n=this.getAttribute("href");setTimeout((function(){i.addClass("exiting")}),600),setTimeout((function(){window.location=n}),1100)})),e(".button-next").on("click",(function(i){if(i.preventDefault(),!function(e){var n=jQuery(e);if("yes"==n.data("done-loading"))return!1;n.is("input")||n.is("button");return n.data("done-loading","yes"),n.addClass("merlin__button--loading"),{done:function(){!0,n.attr("disabled",!1)}}}(this))return!1;var t=e(this).data("callback");return!t||void 0===n[t]||(n[t](this),!1)})),e(document).on("change",".js-merlin-demo-import-select",(function(){var n=e(this).val();e(".js-merlin-select-spinner").show(),e.post(merlin_params.ajaxurl,{action:"merlin_update_selected_import_data_info",wpnonce:merlin_params.wpnonce,selected_index:n},(function(n){n.success?e(".js-merlin-drawer-import-content").html(n.data):alert(merlin_params.texts.something_went_wrong),e(".js-merlin-select-spinner").hide()})).fail((function(){e(".js-merlin-select-spinner").hide(),alert(merlin_params.texts.something_went_wrong)}))})),e("input[name=cmsmasters_merlin_license__source_code]").on("change",(function(){const n=e(this).val();e(".cmsmasters-merlin-license__code").slideUp("fast"),e(`.cmsmasters-merlin-license__code.cmsmasters-merlin-license--${n}`).slideDown("fast")}))}function t(){var n,i=e(".merlin__body"),t=e("#child-theme-text");function s(e){void 0!==e.done?(setTimeout((function(){t.addClass("lead")}),0),setTimeout((function(){t.addClass("success"),t.html(e.message)}),600),n()):(t.addClass("lead error"),t.html(e.error))}return{init:function(t){n=function(){setTimeout((function(){e(".merlin__body").addClass("js--finished")}),1500),i.removeClass(drawer_opened),setTimeout((function(){e(".merlin__body").addClass("exiting")}),3500),setTimeout((function(){window.location.href=t.href}),4e3)},jQuery.post(merlin_params.ajaxurl,{action:"merlin_child_theme",wpnonce:merlin_params.wpnonce},s).fail(s)}}}function s(){const n=e(".merlin__body"),i=e(".cmsmasters-merlin-license"),t=e(".cmsmasters-merlin-license__notice");let s;function r(n){void 0!==n.success&&n.success?(setTimeout((function(){t.addClass("lead")}),0),setTimeout((function(){t.addClass("success"),t.html(n.message)}),600),s()):(e(".js-merlin-license-activate-button").removeClass("merlin__button--loading").data("done-loading","no"),i.addClass("has-error"),void 0!==n.error_field&&i.attr("data-error-field",n.error_field),t.addClass("lead error").html(n.message))}return{init:function(o){i.removeClass("has-error").removeAttr("data-error-field"),t.removeClass("lead error"),s=function(){setTimeout((function(){e(".merlin__body").addClass("js--finished")}),1500),n.removeClass(drawer_opened),setTimeout((function(){e(".merlin__body").addClass("exiting")}),3500),setTimeout((function(){window.location.href=o.href}),4e3)},jQuery.post(merlin_params.ajaxurl,{action:"merlin_activate_license",wpnonce:merlin_params.wpnonce,user_name:e("input[name=cmsmasters_merlin_license__user_name]").val(),user_email:e("input[name=cmsmasters_merlin_license__user_email]").val(),source_code:e("input[name=cmsmasters_merlin_license__source_code]:checked").val(),purchase_code:e("input[name=cmsmasters_merlin_license__purchase_code]").val(),envato_elements_token:e("input[name=cmsmasters_merlin_license__envato_elements_token]").val()},r).fail(r)}}}function r(){var n,i,t=e(".merlin__body"),s=0,r="",o="";function a(e){var n=i.find("label");"object"==typeof e&&void 0!==e.message?(n.removeClass("installing success error").addClass(e.message.toLowerCase()),void 0!==e.done&&e.done?c():void 0!==e.url?e.hash==o?(n.removeClass("installing success").addClass("error"),c()):(o=e.hash,jQuery.post(e.url,e,a).fail(a)):c()):l()}function l(){r&&(i.find("input:checkbox").is(":checked")?jQuery.post(merlin_params.ajaxurl,{action:"merlin_plugins",wpnonce:merlin_params.wpnonce,slug:r},a).fail(a):(i.addClass("skipping"),setTimeout(c,300)))}function c(){i&&(i.data("done_item")||(s++,i.data("done_item",1)),i.find(".spinner").css("visibility","hidden"));var t=e(".merlin__drawer--install-plugins li");t.each((function(){var n=e(this);return!!n.data("done_item")||(r=n.data("slug"),i=n,l(),!1)})),s>=t.length&&n()}return{init:function(i){e(".merlin__drawer--install-plugins").addClass("installing"),e(".merlin__drawer--install-plugins").find("input").prop("disabled",!0),n=function(){setTimeout((function(){e(".merlin__body").addClass("js--finished")}),1e3),t.removeClass(drawer_opened),setTimeout((function(){e(".merlin__body").addClass("exiting")}),3e3),setTimeout((function(){window.location.href=i.href}),3500)},c()}}}function o(){var n,i,t,s=e(".merlin__body"),r=0,o="",a="",l=1,c=0;function d(n){var t=i.find("label");"object"==typeof n&&void 0!==n.message?(t.addClass(n.message.toLowerCase()),void 0!==n.num_of_imported_posts&&0<c&&(l="all"===n.num_of_imported_posts?c:n.num_of_imported_posts,_()),void 0!==n.url?n.hash===a?(t.addClass("status--failed"),m()):(a=n.hash,void 0===n.selected_index&&(n.selected_index=e(".js-merlin-demo-import-select").val()||0),jQuery.post(n.url,n,d).fail(d)):(n.done,m())):(console.log(n),t.addClass("status--error"),m())}function m(){var t=!1;i&&(i.data("done_item")||(r++,i.data("done_item",1)),i.find(".spinner").css("visibility","hidden"));var s=e(".merlin__drawer--import-content__list-item");e(".merlin__drawer--import-content__list-item input:checked");s.each((function(){""==o||t?(o=e(this).data("content"),i=e(this),o&&(i.find("input:checkbox").is(":checked")?jQuery.post(merlin_params.ajaxurl,{action:"merlin_content",wpnonce:merlin_params.wpnonce,content:o,selected_index:e(".js-merlin-demo-import-select").val()||0},d).fail(d):(i.addClass("skipping"),setTimeout(m,300))),t=!1):e(this).data("content")==o&&(t=!0)})),r>=s.length&&n()}function _(){const n=parseInt(e(".js-merlin-progress-bar-percentage").html());let i=l/c*100;n>i&&(i=n),e(".js-merlin-progress-bar").css("width",i+"%");var s,r,o,a=(s=i,r=0,o=99,Math.min(o,Math.max(r,s)));e(".js-merlin-progress-bar-percentage").html(Math.round(a)+"%"),1==l/c&&clearInterval(t)}return{init:function(i){e(".merlin__drawer--import-content").addClass("installing"),e(".merlin__drawer--import-content").find("input").prop("disabled",!0),n=function(){e.post(merlin_params.ajaxurl,{action:"merlin_import_finished",wpnonce:merlin_params.wpnonce,selected_index:e(".js-merlin-demo-import-select").val()||0}),setTimeout((function(){e(".js-merlin-progress-bar-percentage").html("100%")}),100),setTimeout((function(){s.removeClass(drawer_opened)}),500),setTimeout((function(){e(".merlin__body").addClass("js--finished")}),1500),setTimeout((function(){e(".merlin__body").addClass("exiting")}),3400),setTimeout((function(){window.location.href=i.href}),4e3)},function(){if(!e(".merlin__drawer--import-content__list-item .checkbox-content").is(":checked"))return!1;jQuery.post(merlin_params.ajaxurl,{action:"merlin_get_total_content_import_items",wpnonce:merlin_params.wpnonce,selected_index:e(".js-merlin-demo-import-select").val()||0},(function(e){0<(c=e.data)&&(_(),t=setInterval((function(){l+=c/500,_()}),1e3))}))}(),m()}}}return{init:function(){this,e(i)},callback:function(e){console.log(e),console.log(this)}}}(jQuery);Merlin.init();